# Debug Stun Issue - Thờ<PERSON> gian tăng từ 2s lên 3s

## Vấn đề hiện tại
- <PERSON><PERSON> 5 giây
- <PERSON>u 3 giây (còn lại 2 giây)
- <PERSON><PERSON> thêm lần nữa
- Thời gian tăng từ 2 giây lên 3 giây thay vì giữ nguyên 2 giây

## Debug logging đã thêm

### 1. EffectList.java
```java
// <PERSON>hi bắt đầu thêm debuff
LOGGER.info("ATTEMPTING TO ADD DEBUFF: " + skill.getName() + " (ID:" + skill.getId() + ", AbnormalType:" + skill.getAbnormalType() + ", Time:" + info.getAbnormalTime() + "s)");

// Trong logic stacking
LOGGER.info("=== DEBUFF STACKING DEBUG ===");
LOGGER.info("Existing: " + existingSkill.getName() + " (ID:" + existingSkill.getId() + ", Level:" + existingSkill.getAbnormalLevel() + ", AbnormalType:" + existingSkill.getAbnormalType() + ", SubordinationType:" + existingSkill.getSubordinationAbnormalType() + ")");
LOGGER.info("  - Original time: " + existingAbnormalTime + "s, Our calc remaining: " + existingRemainingTime + "s, BuffInfo remaining: " + buffInfoRemainingTime + "s");
LOGGER.info("New: " + skill.getName() + " (ID:" + skill.getId() + ", Level:" + skill.getAbnormalLevel() + ", AbnormalType:" + skill.getAbnormalType() + ", SubordinationType:" + skill.getSubordinationAbnormalType() + ")");
LOGGER.info("  - New time: " + newAbnormalTime + "s");

// Kết quả
LOGGER.info("RESULT: Rejected (existing debuff still active: " + existingRemainingTime + "s remaining, level: " + existingSkill.getAbnormalLevel() + ")");
// hoặc
LOGGER.info("DEBUFF COMPLETELY REJECTED - returning without adding new debuff");

// Khi debuff được thêm thành công
LOGGER.info("DEBUFF SUCCESSFULLY ADDED: " + info.getSkill().getName() + " (Time:" + info.getAbnormalTime() + "s)");
```

### 2. Formulas.java
```java
// Tính toán thời gian abnormal
LOGGER.info("ABNORMAL TIME CALCULATION: " + skill.getName() + " - Original: " + originalTime + "s, Final: " + time + "s" + (skill.getAbnormalType() == AbnormalType.STUN ? " (STUN CAPPED)" : ""));
```

## Các trường hợp có thể xảy ra

### Trường hợp 1: Logic stacking hoạt động đúng
**Log mong đợi:**
```
ATTEMPTING TO ADD DEBUFF: Stun (ID:XXXX, AbnormalType:STUN, Time:5s)
=== DEBUFF STACKING DEBUG ===
Existing: Stun (ID:YYYY, Level:1, AbnormalType:STUN)
  - Original time: 5s, Our calc remaining: 2s, BuffInfo remaining: 2s
New: Stun (ID:XXXX, Level:1, AbnormalType:STUN)
  - New time: 5s
RESULT: Rejected (existing debuff still active: 2s remaining, level: 1)
DEBUFF COMPLETELY REJECTED - returning without adding new debuff
```

### Trường hợp 2: Logic bị bypass
**Log có thể thấy:**
```
ATTEMPTING TO ADD DEBUFF: Stun (ID:XXXX, AbnormalType:STUN, Time:5s)
DEBUFF SUCCESSFULLY ADDED: Stun (Time:5s)
```
**→ Không có debug stacking log = logic bị bypass**

### Trường hợp 3: Tính toán thời gian sai
**Log có thể thấy:**
```
=== DEBUFF STACKING DEBUG ===
Existing: Stun (Original time: 5s, Our calc remaining: 3s, BuffInfo remaining: 2s)
TIME CALCULATION MISMATCH! Our calc: 3s vs BuffInfo: 2s
```

### Trường hợp 4: Có skill khác reset thời gian
**Cần kiểm tra:** Có skill nào gọi `resetAbnormalTime()` không

## Các điểm cần kiểm tra

### 1. Kiểm tra Config.DEVELOPER
Đảm bảo `Config.DEVELOPER = true` để thấy debug log

### 2. Kiểm tra AbnormalType
Đảm bảo cả hai skill stun có cùng AbnormalType.STUN

### 3. Kiểm tra abnormalLevel
Đảm bảo cả hai skill có cùng abnormalLevel

### 4. Kiểm tra subordinationAbnormalType
Đảm bảo không có subordination abnormal type đặc biệt

### 5. Kiểm tra skill effects
Có thể có effect nào đó gọi resetAbnormalTime()

## Test scenario chi tiết

### Bước 1: Stun đầu tiên
1. Cast skill stun 5s
2. **Log mong đợi:**
   ```
   ABNORMAL TIME CALCULATION: Stun - Original: 5s, Final: 5s (STUN CAPPED)
   ATTEMPTING TO ADD DEBUFF: Stun (Time:5s)
   DEBUFF SUCCESSFULLY ADDED: Stun (Time:5s)
   ```

### Bước 2: Đợi 3 giây
1. Kiểm tra thời gian còn lại = 2s

### Bước 3: Stun thứ hai
1. Cast skill stun 5s
2. **Log mong đợi (nếu logic đúng):**
   ```
   ABNORMAL TIME CALCULATION: Stun - Original: 5s, Final: 5s (STUN CAPPED)
   ATTEMPTING TO ADD DEBUFF: Stun (Time:5s)
   === DEBUFF STACKING DEBUG ===
   Existing: Stun (Original time: 5s, Our calc remaining: 2s, BuffInfo remaining: 2s)
   New: Stun (New time: 5s)
   RESULT: Rejected (existing debuff still active: 2s remaining, level: 1)
   DEBUFF COMPLETELY REJECTED - returning without adding new debuff
   ```

### Bước 4: Kiểm tra kết quả
- Nếu thời gian vẫn là 2s → Logic hoạt động đúng
- Nếu thời gian thành 3s → Có vấn đề cần debug thêm

## Các file cần kiểm tra thêm

1. **AbnormalTimeChange.java** - Có thể có effect reset thời gian
2. **BuffInfo.resetAbnormalTime()** - Ai đang gọi method này
3. **Skill effects** - Có effect nào can thiệp vào thời gian không
4. **GameTimeTaskManager** - Có vấn đề với ticks calculation không

## Lưu ý
- Đảm bảo test với cùng skill ID hoặc khác skill ID
- Kiểm tra với cùng caster hoặc khác caster
- Kiểm tra với cùng target
- Đảm bảo không có buff/debuff khác can thiệp
