package org.l2jmobius.gameserver.data.xml;

import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

import org.l2jmobius.commons.util.IXmlReader;
import org.l2jmobius.gameserver.model.GearPenalty;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

public class GearPenaltyData implements IXmlReader
{
	private static final Logger								LOGGER			= Logger.getLogger(GearPenaltyData.class.getName());
	private final Map<String, Map<Integer, GearPenalty>>	_penaltyData	= new HashMap<>();
	private int												_weaponCount	= 0;
	private int												_armorCount		= 0;
	
	protected GearPenaltyData()
	{
		load();
	}
	
	@Override
	public void load()
	{
		_weaponCount = 0;
		_armorCount = 0;
		_penaltyData.clear();
		parseDatapackFile("data/gear_penalty.xml");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _penaltyData.size() + " penalty data entries.");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _weaponCount + " weapon penalties.");
		LOGGER.info(getClass().getSimpleName() + ": Loaded " + _armorCount + " armor penalties.");
	}
	
	@Override
	public void parseDocument(Document doc, File f)
	{
		NodeList list = doc.getElementsByTagName("grade");
		for (int i = 0; i < list.getLength(); i++)
		{
			Node n = list.item(i);
			if (n.getNodeType() == Node.ELEMENT_NODE)
			{
				Element gradeElement = (Element) n;
				String gradeName = gradeElement.getAttribute("name");
				String type = gradeElement.getAttribute("type");
				if (type.equals("Weapon"))
				{
					_weaponCount++;
				}
				else if (type.equals("Armor"))
				{
					_armorCount++;
				}
				Map<Integer, GearPenalty> penaltiesByLevel = new HashMap<>();
				NodeList penaltyList = gradeElement.getElementsByTagName("range");
				for (int j = 0; j < penaltyList.getLength(); j++)
				{
					Element penaltyElement = (Element) penaltyList.item(j);
					int minLevel = Integer.parseInt(penaltyElement.getAttribute("min_level"));
					int maxLevel = Integer.parseInt(penaltyElement.getAttribute("max_level"));
					double pveReduction = Double.parseDouble(penaltyElement.getAttribute("pve_reduction"));
					double pvpReduction = Double.parseDouble(penaltyElement.getAttribute("pvp_reduction"));
					int pieces = penaltyElement.hasAttribute("pieces") ? Integer.parseInt(penaltyElement.getAttribute("pieces")) : 0;
					GearPenalty penalty;
					if (type.equals("Armor"))
					{
						penalty = new GearPenalty(minLevel, maxLevel, pveReduction, pvpReduction, pieces);
					}
					else
					{
						penalty = new GearPenalty(minLevel, maxLevel, pveReduction, pvpReduction);
					}
					for (int level = minLevel; level <= maxLevel; level++)
					{
						penaltiesByLevel.put(level, penalty);
					}
				}
				_penaltyData.put(gradeName + "-" + type, penaltiesByLevel);
			}
		}
	}
	
	public GearPenalty getPenalty(String grade, String type, int level, int pieces)
	{
		Map<Integer, GearPenalty> penalties = _penaltyData.get(grade + "-" + type);
		if (penalties != null)
		{
			if (type.equals("Armor"))
			{
				GearPenalty bestPenalty = null;
				for (int i = pieces; i >= 1; i--)
				{
					GearPenalty penalty = penalties.get(level);
					if (penalty != null && penalty.getPieces() == i)
					{
						bestPenalty = penalty;
						break;
					}
				}
				return bestPenalty;
			}
			else if (type.equals("Weapon"))
			{
				return penalties.get(level);
			}
		}
		return null;
	}
	
	public static GearPenaltyData getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final GearPenaltyData _instance = new GearPenaltyData();
	}
}
