/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.model.clan.dkp;

/**
 * <AUTHOR>
 */
public enum DkpEvent
{
	// Epic
	QUEEN_ANT(29001, "Queen Ant", false),
	CORE(29006, "Core", false),
	ORFEN(29014, "Orfen", false),
	BAIUM(29020, "Baium", false),
	ZAKEN(29022, "<PERSON>aken", false),
	ANTHARAS(29068, "<PERSON><PERSON><PERSON>", false),
	FRINTEZZA(29047, "Frintez<PERSON>", false),
	ANAKIM(25286, "<PERSON><PERSON>", false),
	L<PERSON>IT<PERSON>(25283, "Lilith", false),
	// Raid Boss Level 78
	QUEEN_ANT_DRONE_PRIEST(25738, "Queen Ant Drone Priest", false),
	ANGLE_PRIEST_OF_BAIUM(25739, "Angel Priest of Baium", false),
	PRIEST_OF_CORE_DECAR(25742, "Priest of Core Decar", false),
	PRIEST_OF_LORD_IPOS(25743, "Priest Orfen Lord Ipos", false),
	GARIOTT(25779, "Gariott", false),
	VARBASION(25780, "Varbasion", false),
	VARMONI(25781, "Varmoni", false),
	OVERLORD_MUSCEL(25782, "Overlord Muscel", false),
	// Raid Boss Level 80
	HEKATON_PRESS(25140, "Hekaton Press", false),
	GIANT_MARPANAK(25162, "Giant Marpanak", false),
	GORGOLOS(25467, "Gorgolos", false),
	LAST_TITAN_UTENUS(25470, "Last Titan Utenus", false),
	BLODDY_PRIEST_RUDELTO(25073, "Bloody Priest Rudelto", false),
	ANTHARAS_PRIEST_CLOE(25109, "Antharas Priest Cloe", false),
	KERNON(25054, "Kernon", false),
	KORIM(25092, "Korim", false),
	LONGHORN_GOLKONDA(25126, "Longhorn Golkonda", false),
	FIRE_OF_WRATH_SHURIEL(25143, "Fire of Wrath Shuriel", false),
	DEATH_LORD_HALLATE(25220, "Death Lord Hallate", false),
	ENMITY_GHOST_RAMDAL(25444, "Enmity Ghost Ramdal", false),
	IMMORTAL_SAVIOR_MARDIL(25447, "Immortal Savior Mardil", false),
	CHERUB_GALAXIA(25450, "Cherub Galaxia", false),
	// Sieges
	CASTLE_SIEGE(1_000_000, "Castle Siege", false),
	FORT_SIEGE(1_000_001, "Fort Siege", false),
	// Clan Arena
	CLAN_ARENA(1_000_002, "Clan Arena", false),
	// Dummy
	LEADER_MODIFICATION(10_000_000, "Leader Modification", true),
	DKP_SHOP_PURCHASE(10_000_001, "Shop Purchase", true),
	POINTS_TRADE(10_000_002, "Points Trade", true),
	DKP_AUCTION_WIN(10_000_003, "Auction Win", true),
	MASS_LEADER_MODIFICATION(10_000_004, "Mass Leader Modification", true);
	
	private final int		_id;
	private final String	_name;
	private final boolean	_isDummy;
	
	DkpEvent(int id, String name, boolean isDummy)
	{
		_id = id;
		_name = name;
		_isDummy = isDummy;
	}
	
	public static DkpEvent getDkpEventById(int id)
	{
		for (DkpEvent raid : values())
		{
			if ((raid.getId() > 0) && (raid.getId() == id))
			{
				return raid;
			}
		}
		return null;
	}
	
	public int getId()
	{
		return _id;
	}
	
	public String getName()
	{
		return _name;
	}
	
	public boolean isDummy()
	{
		return _isDummy;
	}
}
