/*
 * Copyright (c) 2013 L2jMobius
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.l2jmobius.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.util.Rnd;
import org.l2jmobius.gameserver.model.actor.Player;

/**
 * Gift Code Manager
 * <AUTHOR>
 */
public class GiftCodeManager
{
	private static final Logger LOGGER = Logger.getLogger(GiftCodeManager.class.getName());
	
	// SQL Queries
	private static final String INSERT_GIFT_CODE = "INSERT INTO gift_codes (code) VALUES (?)";
	private static final String CHECK_GIFT_CODE = "SELECT * FROM gift_codes WHERE code = ?";
	private static final String USE_GIFT_CODE = "UPDATE gift_codes SET used = 1, used_by = ?, used_date = ? WHERE code = ? AND used = 0";
	private static final String DELETE_GIFT_CODE = "DELETE FROM gift_codes WHERE code = ?";
	private static final String CHECK_PLAYER_USAGE = "SELECT COUNT(*) FROM gift_codes WHERE used_by = ? AND used = 1";

	// Rate limiting - max 1 gift code per player per minute
	private final Map<Integer, Long> _lastUsageTime = new ConcurrentHashMap<>();
	private static final long USAGE_COOLDOWN = 60000; // 1 minute
	
	// Gift code rewards
	private static final int[] REWARD_ITEMS = {94072, 94073, 49487};
	private static final int[] REWARD_COUNTS = {5, 5, 50};
	private static final int PREMIUM_DAYS = 7;
	
	protected GiftCodeManager()
	{
		LOGGER.info("GiftCodeManager: Initialized.");
	}
	
	/**
	 * Generate a random 6-character gift code
	 * @return the generated code
	 */
	public String generateGiftCode()
	{
		final String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
		final StringBuilder code = new StringBuilder();
		
		for (int i = 0; i < 6; i++)
		{
			code.append(chars.charAt(Rnd.get(chars.length())));
		}
		
		return code.toString();
	}
	
	/**
	 * Create a new gift code and store it in database
	 * @return the created gift code
	 */
	public String createGiftCode()
	{
		String code;
		boolean codeExists = true;
		
		// Generate unique code
		do
		{
			code = generateGiftCode();
			codeExists = checkCodeExists(code);
		}
		while (codeExists);
		
		// Store in database
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(INSERT_GIFT_CODE))
		{
			ps.setString(1, code);
			ps.executeUpdate();
			LOGGER.info("GiftCodeManager: Created new gift code: " + code);
			return code;
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error creating gift code: " + e.getMessage());
			return null;
		}
	}
	
	/**
	 * Check if a gift code exists in database
	 * @param code the code to check
	 * @return true if code exists
	 */
	private boolean checkCodeExists(String code)
	{
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(CHECK_GIFT_CODE))
		{
			ps.setString(1, code);
			try (ResultSet rs = ps.executeQuery())
			{
				return rs.next();
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error checking gift code: " + e.getMessage());
			return true; // Assume exists to be safe
		}
	}
	
	/**
	 * Use a gift code and give rewards to player
	 * @param player the player using the code
	 * @param code the gift code
	 * @return result message
	 */
	public String useGiftCode(Player player, String code)
	{
		if (code == null || code.length() != 6)
		{
			return "Mã gift code không hợp lệ!";
		}
		
		code = code.toUpperCase().trim();
		
		// Check if code exists and is not used
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(CHECK_GIFT_CODE))
		{
			ps.setString(1, code);
			try (ResultSet rs = ps.executeQuery())
			{
				if (!rs.next())
				{
					return "Mã gift code không tồn tại!";
				}
				
				if (rs.getInt("used") == 1)
				{
					return "Mã gift code đã được sử dụng!";
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error checking gift code: " + e.getMessage());
			return "Lỗi hệ thống, vui lòng thử lại!";
		}
		
		// Mark code as used
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(USE_GIFT_CODE))
		{
			ps.setInt(1, player.getObjectId());
			ps.setTimestamp(2, new Timestamp(System.currentTimeMillis()));
			ps.setString(3, code);
			
			int updated = ps.executeUpdate();
			if (updated == 0)
			{
				return "Mã gift code đã được sử dụng bởi người khác!";
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error using gift code: " + e.getMessage());
			return "Lỗi hệ thống, vui lòng thử lại!";
		}
		
		// Give rewards
		giveRewards(player);
		
		LOGGER.info("GiftCodeManager: Player " + player.getName() + " used gift code: " + code);
		return "Chúc mừng! Bạn đã nhận được phần thưởng từ gift code!";
	}
	
	/**
	 * Give rewards to player
	 * @param player the player to give rewards
	 */
	private void giveRewards(Player player)
	{
		// Add premium time (7 days)
		PremiumManager.getInstance().addPremiumTime(player.getAccountName(), PREMIUM_DAYS, TimeUnit.DAYS);
		player.sendMessage("Bạn đã nhận được " + PREMIUM_DAYS + " ngày Premium!");
		
		// Add items
		for (int i = 0; i < REWARD_ITEMS.length; i++)
		{
			player.addItem("GiftCode", REWARD_ITEMS[i], REWARD_COUNTS[i], player, true);
		}
		
		player.sendMessage("Bạn đã nhận được các vật phẩm từ gift code!");
	}
	
	public static GiftCodeManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final GiftCodeManager INSTANCE = new GiftCodeManager();
	}
}
