/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.clientpackets.costume;

import java.time.Duration;
import java.time.Instant;

import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.CostumeCollection;
import org.l2jmobius.gameserver.model.costumes.CostumeCollectionData;
import org.l2jmobius.gameserver.model.costumes.CostumeManager;
import org.l2jmobius.gameserver.network.SystemMessageId;
import org.l2jmobius.gameserver.network.clientpackets.ClientPacket;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeCollectionSkillActive;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeListFull;

/**
 * <AUTHOR>
 */
public class ExRequestCostumeCollectSkillActive extends ClientPacket
{
	private int collectionId;
	
	@Override
	protected void readImpl()
	{
		collectionId = readInt();
	}
	
	@Override
	protected void runImpl()
	{
		final Player player = getPlayer();
		if (player == null)
		{
			return;
		}
		if (clickActivateCollection(player, collectionId))
		{
			player.sendPacket(new ExCostumeCollectionSkillActive());
			player.sendPacket(new ExSendCostumeListFull());
		}
	}
	
	public boolean clickActivateCollection(Player player, int collectionId)
	{
		final CostumeCollectionData activeCollection = player.getActiveCostumeCollection();
		final CostumeCollection collection = CostumeManager.getInstance().getCollections().get(collectionId);
		if ((collection != null) && CostumeManager.getInstance().hasAllCostumes(player, collection))
		{
			if (activeCollection != null)
			{
				if (activeCollection.getId() == collectionId)
				{
					player.sendPacket(SystemMessageId.COLLECTION_EFFECT_HAS_ALREADY_BEEN_ACTIVATED);
					return false;
				}
				int remainingTime = activeCollection.getReuseTime();
				if (remainingTime > 0)
				{
					player.sendMessage("You can select another collection effect in " + remainingTime + " seconds.");
					return false;
				}
			}
			final int reuseTime = (int) Instant.now().plus(Duration.ofMinutes(10)).getEpochSecond();
			CostumeCollectionData.createCostumeInDB(player, collectionId, reuseTime);
			final CostumeCollectionData newCollection = CostumeCollectionData.of(player, collectionId, reuseTime);
			player.setActiveCostumeCollection(newCollection);
			player.addSkill(collection.skill(), false);
			return true;
		}
		player.sendPacket(SystemMessageId.THE_COLLECTION_IS_NOT_FULL_YOU_CANNOT_ACTIVATE_THE_EFFECT);
		return false;
	}
}