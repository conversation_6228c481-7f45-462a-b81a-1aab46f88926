/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.LinkedList;
import java.util.List;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.PacketLogger;
import org.l2jmobius.gameserver.network.ServerPackets;

/**
 * <AUTHOR>
 */
public class ExRequestPartyMatchingHistory extends ServerPacket
{
	private static final String			GET_HISTORY	= "SELECT title, leader FROM party_matching_history ORDER BY id DESC LIMIT 100";	// Maximum size according to retail is 100.
	private final List<HistoryHolder>	_history	= new LinkedList<>();
	
	public ExRequestPartyMatchingHistory()
	{
		// TODO: Cache this every 30 minutes.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement(GET_HISTORY); ResultSet rset = statement.executeQuery())
		{
			while (rset.next())
			{
				_history.add(new HistoryHolder(rset.getString("title"), rset.getString("leader")));
			}
		}
		catch (Exception e)
		{
			PacketLogger.warning("ExRequestPartyMatchingHistory: Could not load data: " + e.getMessage());
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_PARTY_MATCHING_ROOM_HISTORY.writeId(this, buffer);
		buffer.writeInt(_history.size());
		for (HistoryHolder holder : _history)
		{
			buffer.writeString(holder.getTitle());
			buffer.writeString(holder.getLeader());
		}
	}
	
	private class HistoryHolder
	{
		private final String	_title;
		private final String	_leader;
		
		public HistoryHolder(String title, String leader)
		{
			_title = title;
			_leader = leader;
		}
		
		public String getTitle()
		{
			return _title;
		}
		
		public String getLeader()
		{
			return _leader;
		}
	}
}
