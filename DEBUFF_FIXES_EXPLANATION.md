# Sửa đổi hệ thống Debuff - Kh<PERSON><PERSON> phục lỗi thời gian và giới hạn Stun

## Vấn đề đã được khắc phục

### 1. **Lỗi tính toán thời gian (Time Calculation Bug)**

**Vấn đề:** Khi bị stun 7 giây và bị stun lại sau 3 giây, thời gian còn lại bị đóng băng/tạm dừng thay vì tiếp tục đếm ngược.

**Nguyên nhân:** Method `BuffInfo.getTime()` có thể cho kết quả không nhất quán khi được gọi nhiều lần trong cùng một thời điểm, gây ra vấn đề với logic stacking.

**Giải pháp đã triển khai:**
- Tính toán thời gian còn lại bằng cách riêng để đảm bảo tính nhất quán
- So s<PERSON>h với kết quả của `BuffInfo.getTime()` để phát hiện sự khác biệt
- Thêm logging chi tiết để debug vấn đề thời gian

```java
// Calculate remaining time more accurately to avoid timing issues
final int existingAbnormalTime = existingInfo.getAbnormalTime();
final int existingPeriodStartTicks = existingInfo.getPeriodStartTicks();
final int currentGameTicks = GameTimeTaskManager.getInstance().getGameTicks();
final int ticksElapsed = currentGameTicks - existingPeriodStartTicks;
final int secondsElapsed = ticksElapsed / GameTimeTaskManager.TICKS_PER_SECOND;

// Calculate remaining time using our own calculation to ensure consistency
final int existingRemainingTime = Math.max(0, existingAbnormalTime - secondsElapsed);

// Also get the BuffInfo's calculation for comparison
final int buffInfoRemainingTime = existingInfo.getTime();
```

### 2. **Giới hạn thời gian Stun tối đa 5 giây**

**Yêu cầu:** Tất cả skill stun không được vượt quá 5 giây, bất kể thời gian gốc là bao nhiêu.

**Giải pháp đã triển khai:**
- Sửa đổi `Formulas.calcEffectAbnormalTime()` để kiểm tra và giới hạn stun
- Áp dụng cho tất cả skill có `AbnormalType.STUN`
- Giới hạn được áp dụng sau khi tính toán mastery skill

```java
// Cap all stun effects to maximum 5 seconds
if ((skill != null) && (skill.getAbnormalType() == AbnormalType.STUN) && (time > 5))
{
    time = 5;
}
```

## Thay đổi trong code

### File: `EffectList.java`
- **Cải thiện tính toán thời gian:** Sử dụng tính toán riêng dựa trên game ticks
- **Debug logging nâng cao:** Hiển thị cả hai cách tính toán thời gian
- **Phát hiện lỗi thời gian:** Warning khi có sự khác biệt giữa các cách tính

### File: `Formulas.java`
- **Giới hạn stun 5 giây:** Thêm logic kiểm tra và giới hạn thời gian stun
- **Áp dụng sau mastery:** Đảm bảo mastery skill vẫn hoạt động nhưng không vượt quá 5s

## Kết quả mong đợi

### Trước khi sửa:
1. **Vấn đề thời gian:**
   - Stun 7s → sau 3s bị stun lại → thời gian bị đóng băng
   - Có thể dẫn đến stun kéo dài hơn dự kiến

2. **Không có giới hạn stun:**
   - Skill stun 10s → Player bị stun 10s
   - Skill stun 15s với mastery → Player bị stun 30s

### Sau khi sửa:
1. **Thời gian chính xác:**
   - Stun 7s → sau 3s bị stun lại → thời gian tiếp tục đếm ngược chính xác
   - Debuff mới bị từ chối vì còn 4s

2. **Giới hạn stun 5s:**
   - Skill stun 10s → Player chỉ bị stun 5s
   - Skill stun 15s với mastery → Player chỉ bị stun 5s (không phải 30s)

## Test Cases

### Test Case 1: Kiểm tra tính toán thời gian
**Setup:**
- Skill A: Stun 7s
- Skill B: Stun 5s (cùng level)

**Scenario:**
1. Cast Skill A → Target bị stun 7s
2. Sau 3s, cast Skill B
3. **Kết quả mong đợi:** 
   - Debug log hiển thị: "Our calc remaining: 4s, BuffInfo remaining: 4s"
   - Skill B bị từ chối
   - Target tiếp tục stun 4s

### Test Case 2: Kiểm tra giới hạn stun 5s
**Setup:**
- Skill có abnormalTime = 10s, abnormalType = STUN

**Scenario:**
1. Cast skill lên target
2. **Kết quả mong đợi:** Target chỉ bị stun 5s (không phải 10s)

### Test Case 3: Kiểm tra mastery + giới hạn stun
**Setup:**
- Skill có abnormalTime = 4s, abnormalType = STUN
- Caster có skill mastery (x2 thời gian)

**Scenario:**
1. Cast skill lên target
2. **Kết quả mong đợi:** 
   - Thời gian tính toán: 4s * 2 = 8s
   - Sau giới hạn: 5s
   - Target bị stun 5s

## Debug Logging

Khi `Config.DEVELOPER = true`, hệ thống sẽ log:

```
=== DEBUFF STACKING DEBUG ===
Existing: Stun (ID:4726, Level:1, AbnormalType:STUN, SubordinationType:NONE)
  - Original time: 7s, Our calc remaining: 4s, BuffInfo remaining: 4s
  - Period start ticks: 12345, Current ticks: 12645, Elapsed: 300 ticks (3s)
New: Stun (ID:4744, Level:1, AbnormalType:STUN, SubordinationType:NONE)
  - New time: 5s
Same caster: false
RESULT: Rejected (existing debuff still active: 4s remaining, level: 1)
=== END DEBUG ===
```

**Nếu có lỗi thời gian:**
```
TIME CALCULATION MISMATCH! Our calc: 4s vs BuffInfo: 6s
```

## Lưu ý quan trọng

1. **Tính toán thời gian:** Sử dụng cách tính riêng để tránh vấn đề timing
2. **Giới hạn stun:** Áp dụng cho TẤT CẢ skill stun, không có ngoại lệ
3. **Mastery skill:** Vẫn hoạt động nhưng không thể vượt quá 5s cho stun
4. **Debug logging:** Giúp phát hiện và khắc phục vấn đề thời gian
5. **Tương thích ngược:** Không ảnh hưởng đến buff/debuff khác
