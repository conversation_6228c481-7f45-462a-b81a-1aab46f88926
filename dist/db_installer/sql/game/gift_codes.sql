DROP TABLE IF EXISTS `gift_codes`;
CREATE TABLE IF NOT EXISTS `gift_codes` (
  `code` VARCHAR(6) NOT NULL,
  `used` TINYINT(1) NOT NULL DEFAULT 0,
  `created_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `used_by` INT DEFAULT NULL,
  `used_date` TIMESTAMP NULL DEFAULT NULL,
  `player_ip` VARCHAR(45) DEFAULT NULL,
  PRIMARY KEY (`code`),
  <PERSON>EY `used` (`used`),
  KEY `used_by` (`used_by`),
  <PERSON>EY `used_date` (`used_date`),
  KEY `player_ip` (`player_ip`)
) DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Create index for better performance
CREATE INDEX idx_gift_codes_usage ON gift_codes (used_by, used_date);

-- Create table for gift code usage history (for admin tracking)
DROP TABLE IF EXISTS `gift_code_history`;
CREATE TABLE IF NOT EXISTS `gift_code_history` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `code` VARCHAR(6) NOT NULL,
  `player_id` INT NOT NULL,
  `player_name` VARCHAR(35) NOT NULL,
  `player_ip` VARCHAR(45) DEFAULT NULL,
  `used_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `player_id` (`player_id`),
  KEY `used_date` (`used_date`),
  KEY `player_ip` (`player_ip`)
) DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
