/*
 * Copyright (c) 2013 L2jMobius
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package handlers.admincommandhandlers;

import java.util.StringTokenizer;

import org.l2jmobius.gameserver.handler.IAdminCommandHandler;
import org.l2jmobius.gameserver.instancemanager.GiftCodeManager;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.util.BuilderUtil;

/**
 * Admin Gift Code command handler.
 * <AUTHOR>
 */
public class AdminGiftCode implements IAdminCommandHandler
{
	private static final String[] ADMIN_COMMANDS =
	{
		"admin_giftcode",
		"admin_creategiftcode",
		"admin_giftcode_create"
	};
	
	@Override
	public boolean useAdminCommand(String command, Player activeChar)
	{
		final StringTokenizer st = new StringTokenizer(command, " ");
		final String actualCommand = st.nextToken();
		
		switch (actualCommand.toLowerCase())
		{
			case "admin_giftcode":
			case "admin_creategiftcode":
			case "admin_giftcode_create":
			{
				if (st.hasMoreTokens())
				{
					final String param = st.nextToken();
					try
					{
						final int count = Integer.parseInt(param);
						if (count <= 0 || count > 100)
						{
							BuilderUtil.sendSysMessage(activeChar, "Số lượng phải từ 1 đến 100!");
							return false;
						}
						
						BuilderUtil.sendSysMessage(activeChar, "Đang tạo " + count + " gift codes...");
						
						for (int i = 0; i < count; i++)
						{
							final String giftCode = GiftCodeManager.getInstance().createGiftCode();
							if (giftCode != null)
							{
								BuilderUtil.sendSysMessage(activeChar, "Gift Code " + (i + 1) + ": " + giftCode);
							}
							else
							{
								BuilderUtil.sendSysMessage(activeChar, "Lỗi tạo gift code " + (i + 1));
							}
						}
						
						BuilderUtil.sendSysMessage(activeChar, "Hoàn thành tạo " + count + " gift codes!");
					}
					catch (NumberFormatException e)
					{
						BuilderUtil.sendSysMessage(activeChar, "Số lượng không hợp lệ!");
						return false;
					}
				}
				else
				{
					// Create single gift code
					final String giftCode = GiftCodeManager.getInstance().createGiftCode();
					if (giftCode != null)
					{
						BuilderUtil.sendSysMessage(activeChar, "Gift Code đã tạo: " + giftCode);
						BuilderUtil.sendSysMessage(activeChar, "Phần thưởng: Premium 7 ngày + Items");
					}
					else
					{
						BuilderUtil.sendSysMessage(activeChar, "Lỗi tạo gift code!");
					}
				}
				break;
			}
		}
		
		return true;
	}
	
	@Override
	public String[] getAdminCommandList()
	{
		return ADMIN_COMMANDS;
	}
}
