<?xml version="1.0" encoding="UTF-8"?>
<event name="Girls With Matches" active="31 12 2024-31 01 2025" enableShrines="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../xsd/eventConfig.xsd">
	<droplist>
		<add item="71035" min="1" max="1" chance="0.1%" minLevel="1" maxLevel="69" /> <!-- Cooking Ingredient -->
		<add item="71035" min="1" max="1" chance="0.2%" minLevel="70" maxLevel="80" /> <!-- Cooking Ingredient -->
		<add item="71035" min="1" max="1" chance="0.4%" minLevel="80" maxLevel="99" /> <!-- Cooking Ingredient -->
	</droplist>
	<spawnlist>
		<add npc="9024" x="83688" y="147976" z="-3407" heading="38182" /> <!-- Older Sister - Daydreamer Match Girl -->
		<!-- <add npc="9025" x="83688" y="148024" z="-3407" heading="34969" /> Younger Sister - Hungry Match Girl -->
		<add npc="18928" x="83432" y="148376" z="-3407" heading="33966" /> <!-- Bonfire -->
	</spawnlist>
	<destroyItemsOnEnd>
		<item id="71035" /> <!-- Cooking Ingredient -->
		<item id="71031" /> <!-- Regular Dish (Time-limited) -->
		<item id="71032" /> <!-- Very Delicious Dish (Time-limited) -->
		<item id="95030" /> <!-- Pack Helmet -->
		<item id="95031" /> <!-- Pack Armor - Heavy -->
		<item id="95034" /> <!-- Pack Armor - Heavy -->
		<item id="95035" /> <!-- Pack Armor - Heavy -->
		<item id="95032" /> <!-- Pack Gloves -->
		<item id="95033" /> <!-- Pack Boots -->
	</destroyItemsOnEnd>
	<messages>
		<add type="onEnd" text="Girls With Matches: Event end!" />
		<add type="onEnter" text="Girls With Matches: Event ongoing!" />
	</messages>
</event>