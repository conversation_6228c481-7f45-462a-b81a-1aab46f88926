<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="xsd/ResidenceFunctions.xsd">
	<!-- Clan hall functions -->
	<function id="1" type="HP_REGEN">
		<function level="1" costId="57" costCount="700" duration="1days" value="1.2" />
		<function level="2" costId="57" costCount="800" duration="1days" value="1.4" />
		<function level="3" costId="57" costCount="900" duration="1days" value="1.6" />
		<function level="4" costId="57" costCount="1000" duration="1days" value="1.8" />
		<function level="5" costId="57" costCount="3500" duration="3days" value="2.0" />
		<function level="6" costId="57" costCount="1500" duration="1days" value="2.2" />
		<function level="7" costId="57" costCount="1750" duration="1days" value="2.4" />
		<function level="8" costId="57" costCount="2000" duration="1days" value="2.6" />
		<function level="9" costId="57" costCount="2250" duration="1days" value="2.8" />
		<function level="10" costId="57" costCount="7500" duration="3days" value="3.0" />
		<function level="11" costId="57" costCount="3250" duration="1days" value="3.2" />
		<function level="12" costId="57" costCount="3750" duration="1days" value="3.4" />
		<function level="13" costId="57" costCount="4250" duration="1days" value="3.6" />
		<function level="14" costId="57" costCount="4750" duration="1days" value="3.8" />
		<function level="15" costId="57" costCount="15500" duration="3days" value="4.0" />
		<function level="16" costId="57" costCount="6250" duration="1days" value="4.2" />
		<function level="17" costId="57" costCount="7000" duration="1days" value="4.4" />
		<function level="18" costId="57" costCount="7750" duration="1days" value="4.6" />
		<function level="19" costId="57" costCount="8500" duration="1days" value="4.8" />
		<function level="20" costId="57" costCount="26500" duration="3days" value="5.0" />
	</function>
	<function id="2" type="MP_REGEN">
		<function level="1" costId="57" costCount="2000" duration="1days" value="1.05" />
		<function level="2" costId="57" costCount="7500" duration="2days" value="1.10" />
		<function level="3" costId="57" costCount="6500" duration="1days" value="1.15" />
		<function level="4" costId="57" costCount="17000" duration="2days" value="1.20" />
		<function level="5" costId="57" costCount="12000" duration="1days" value="1.25" />
		<function level="6" costId="57" costCount="27500" duration="2days" value="1.30" />
		<function level="7" costId="57" costCount="18500" duration="1days" value="1.35" />
		<function level="8" costId="57" costCount="40000" duration="2days" value="1.40" />
		<function level="9" costId="57" costCount="26000" duration="2days" value="1.45" />
		<function level="10" costId="57" costCount="55000" duration="2days" value="1.50" />
	</function>
	<function id="3" type="EXP_RESTORE">
		<function level="1" costId="57" costCount="3000" duration="1days" value="5" />
		<function level="2" costId="57" costCount="6000" duration="1days" value="10" />
		<function level="3" costId="57" costCount="9000" duration="1days" value="15" />
		<function level="4" costId="57" costCount="35000" duration="3days" value="20" />
		<function level="5" costId="57" costCount="15000" duration="1days" value="25" />
		<function level="6" costId="57" costCount="18000" duration="1days" value="30" />
		<function level="7" costId="57" costCount="21000" duration="1days" value="35" />
		<function level="8" costId="57" costCount="70000" duration="3days" value="40" />
		<function level="9" costId="57" costCount="27000" duration="1days" value="45" />
		<function level="10" costId="57" costCount="30000" duration="1days" value="50" />
		<function level="11" costId="57" costCount="33000" duration="1days" value="55" />
		<function level="12" costId="57" costCount="105000" duration="3days" value="60" />
	</function>
	<function id="4" type="TELEPORT">
		<function level="1" costId="57" costCount="7000" duration="7days" />
		<function level="2" costId="57" costCount="6000" duration="3days" />
	</function>
	<function id="5" type="BUFF">
		<function level="1" costId="57" costCount="5000" duration="1days" />
		<function level="2" costId="57" costCount="5000" duration="1days" />
		<function level="3" costId="57" costCount="5000" duration="1days" />
		<function level="4" costId="57" costCount="5000" duration="1days" />
	</function>
	<function id="6" type="ITEM">
		<function level="1" costId="57" costCount="30000" duration="1days" />
		<function level="2" costId="57" costCount="70000" duration="1days" />
		<function level="3" costId="57" costCount="140000" duration="1days" />
	</function>
	<function id="7" type="CURTAIN">
		<function level="1" costId="57" costCount="2000" duration="7days" />
		<function level="2" costId="57" costCount="2500" duration="7days" />
	</function>
	<function id="8" type="PLATFORM">
		<function level="1" costId="57" costCount="1300" duration="3days" />
		<function level="2" costId="57" costCount="4000" duration="3days" />
	</function>
</list>