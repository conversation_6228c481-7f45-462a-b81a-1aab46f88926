<html noscrollbar>
<title>Community Board</title>
<body><br>
<table width=755>
<tr>
<center><td align=center valign=top>
<table border=0 cellpadding=0 cellspacing=0 width=769 height=492 background="l2ui_ct1.Windows_DF_TooltipBG">
<tr>
<td valign="top" align="center">
<table width=755 height=468>
<tr>
<td width=755>
<table border=0 cellspacing=0 cellpadding=0 width=755>
<tr>
<td height=10></td>
</tr>
<tr>
<td width=260>
<table width=260 height=180>
<tr>
<td valign="top" align="center">
<table border=0 width=260 height=200 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>

<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Name</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="669900">%name%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Class</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%class%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Level</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%level%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Clan</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%clan%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Online Time</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%online_time%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Premium Status</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%PREMIUM_STATUS%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">Premium Duration</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%PREMIUM_DURATION%</font>
</td>
</tr>
</table>

<!-- <table width=255 height=25 bgcolor=333333>
<tr>
<td width=10 align=center valign=center>
<font color="B59A75">Your L-Coin :</font> <font color="FFFFFF">%lcoin_farm%</font>
</td>
<td>
/
</td>
<td width=10 align=left valign=center>
<font color="B59A75">Max L-Coin :</font> <font color="FFFFFF">%max_lcoin_farm%</font>
</td>
</tr>
</table> -->



<!-- <table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">VIP Level</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%VIP%</font>
</td>
</tr>
</table>
<table width=255 height=25 bgcolor=333333>
<tr>
<td width=40 align=right valign=center>
<font color="B59A75">VIP Point</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=40 align=right valign=center>
<font color="FFFFFF">%VIP_Point%</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=40 align=left valign=center>
<font color="B59A75">Next Level</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=40 align=left valign=center>
<font color="FFFFFF">%VIP_Point_Next_Level%</font>
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=90 align=right valign=center>
<font color="B59A75">VIP Duration</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%VIP_Duration%</font>
</td>
</tr>
</table> -->

<!--<table width=260 height=25 bgcolor=333333>
<tr>
<td width=90 align=left valign=center>
<font color="B59A75">Off-Play Duration</font>
</td>
<td width=4 align=center valign=center>
:
</td>
<td width=90 align=left valign=center>
<font color="FFFFFF">%offline_play_time%</font>
</td>
</tr>
</table>-->


</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
<td width=456>
<table width=456 height=180>
<tr>
<td valign="top" align="center">
<table border=0 width=456 height=240 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>
<table width=456 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font name="CreditTextNormal" color="CCFF99">Chào mừng bạn đến với thế giới Lineage II</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=456 height=25 bgcolor=000000>
<tr>
<td width=456 align=left valign=center>
 <font>Hello <font name="CreditTextNormal" color=669900>%name%</font><br1>
     - Sever theo tiêu chí trải nghiệm giải trí đậm chất hoài niệm với lối chơi đơn giản, low rate để mỗi bước phiêu lưu là một niềm vui.<br1>
     - Log Tối đa 5 tài khoản để xây dựng đội hình 1 cách tốt nhất.<br1>
     - Các class hỗ trợ đã được gộp lại tinh gọn, mang đến trải nghiệm mượt mà, dễ tiếp cận.<br1>
     Chúc bạn có những giây phút giải trí vui vẻ.</font>
</td>
</tr>
</table>
<table width=456 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font name="CreditTextNormal" color="CCFF99">Server Rates</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=456 height=25 bgcolor=000000>
<tr>
<td width=456 align=center valign=center>
<table align=center width=456 border=0>
<tr>
<td width=50 align=right><font color=B59A75>XP</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%xp%</font></td>
<td width=50 align=right><font color=B59A75>SP</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%sp%</font></td>
<td width=50 align=right><font color=B59A75>Adena</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%adena_chance%</font></td>
</tr>
<tr>
<td width=50 align=right><font color=B59A75>Drop</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%drop_chance%</font></td>
<td width=50 align=right><font color=B59A75>Spoil</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%spoil_chance%</font></td>
<td width=50 align=right><font color=B59A75>Quest</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x%rate_quest_drop%</font></td>
</tr>

<!-- <tr>
<td width=50 align=right><font color=B59A75>Siege</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x10</font></td>
<td width=50 align=right><font color=B59A75>Manor</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x10</font></td>
<td width=50 align=right><font color=B59A75>Fish</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x10</font></td>
</tr> -->

<!-- <tr>
<td width=50 align=right><font color=B59A75>ClanRep</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x10</font></td>
<td width=50 align=right><font color=B59A75>Hellbound</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x10</font></td>
<td width=50 align=right><font color=B59A75>RaidBoss</font></td>
<td width=5>:</td>
<td width=50><font name="CreditTextNormal" color=FFFFFF>x2</font></td>
</tr> -->


</table>
</td>
</tr>
</table>
<table width=456 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font name="CreditTextNormal" color="CCFF99">Server info</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=456 height=23 bgcolor=000000>
<tr>
<td align=center valign=center width=150>
<font color="B59A75" name="CreditTextNormal">Time: </font><font name="CreditTextNormal"> %time% </font>
</td>
<td align=center valign=center width=150>
<font color="B59A75" name="CreditTextNormal">Online Now: </font><font name="CreditTextNormal"> %online% </font>
</td>
<td align=center valign=center width=150>
<font color="B59A75" name="CreditTextNormal">Offtraders: </font><font name="CreditTextNormal"> %offtrade% </font>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border=0 cellspacing=0 cellpadding=0 width=456>
<tr>
<td height=18></td>
</tr>
<tr>
<td width=260>
<table width=260 height=92>
<tr>
<td valign="top" align="center">
<table border=0 width=260 height=92 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>
<table width=260 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=228 align=center valign=center>
<font color="CCFF99">Social Information</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=260 height=25 bgcolor=000000>
<tr>
<td width=260 height=55 align=center valign=center>
<table width=260 height=25>
<tr>
<td width=53 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_scroll_of_tiger_b">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="url  https://www.facebook.com/profile.php?id=61578138941466" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Facebook
</td>
<td width=53 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_scroll_of_tiger_a">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="url https://l2retrovn.com/" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Website
</td>
<td width=53 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_scroll_of_transformation_traikan_i00">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="url https://discord.com/invite/62QbYvnPRm" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Discord
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
<td width=456>
<table width=456 height=92>
<tr>
<td valign="top" align="center">
<table border=0 width=455 height=92 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=220 align=left valign=top>
<table width=455 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=416 align=center valign=center>
<font color="CCFF99">Main Navigation</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td> 
</tr>
</table>


<table width=455 height=25>
<tr>
<td width=80 align=center valign=top>
    <table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5243">
    <tr>
        <td width=32 height=32 align=center valign=top>
        <button value=" " action="bypass -h _bbstop;info.html" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
        </td>
    </tr>
    </table>
    Server Info
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5861">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsgrandbossstatus" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Grand Boss Status
</td>

<td width=80 align=center valign=top>
    <table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill6019">
    <tr>
        <td width=32 height=32 align=center valign=top>
        <button value=" " action="bypass _bbssearchdropCalc" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
        </td>
    </tr>
    </table>
    Drop Search
</td>

<td width=80 align=center valign=top>
    <table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.etc_treasure_box_i04">
    <tr>
        <td width=32 height=32 align=center valign=top>
        <button value=" " action="bypass _bbshome giftcode" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
        </td>
    </tr>
    </table>
    Gift Code
</td>

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.action011">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbspartymatching" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Party Matching
</td>

<td width=80 align=center valign=top>
    <table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.ability_lock">
    <tr>
        <td width=32 height=32 align=center valign=top>
        <button value=" " action="bypass _bbschangepass" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
        </td>
    </tr>
    </table>
    Password
</td>

</tr>
</table>



</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border=0 cellspacing=0 cellpadding=0 width=736>
<tr>
<td height=18></td>
</tr>
<tr>
<td width=736>
<table width=736 height=60>
<tr>
<td valign="top" align="center">
<table border=0 width=745 height=98 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=736 align=center valign=center>
<table width=740 height=23 background="l2ui_ct1.Windows_DF_Drawer_Bg">
<tr>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
<td width=704 align=center valign=center>
<font color="CCFF99">Navigation</font>
</td>
<td width=16 align=left valign=top>
<img src="L2UI_CT1.RadarMap_DF_iCN_Target01" width=16 height=16></img> 
</td>
</tr>
</table>
<table width=755 height=60 bgcolor=000000>
<tr>

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5243">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop;info.html" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Server Info
</td> -->

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5861">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop;RB.html" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
RaidBoss
</td> -->

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.vam_sleep_1">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _bbstop_settings_menu_autoplayer" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Menu
</td>


<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="branchsys2.br_vitality_day_i00">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass -h _cbDkp" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Clan DKP
</td>

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5739">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsbufferbypass menu" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Buffer
</td> -->

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill6019">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbssearchdropCalc" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Drop Search
</td> -->

<td width=80 align=center valign=top>
    <table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill5739">
    <tr>
        <td width=32 height=32 align=center valign=top>
        <button value=" " action="bypass _bbsbufferbypass menu" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
        </td>
    </tr>
    </table>
    Buffer
</td>

<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="icon.etc_quest_account_reward_i00">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbscustom_referral_home" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Referral System
</td> -->


<!-- <td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.skill6171">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbshowCollection" width=32 height=32 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Collection
</td> -->

<td width=110 align=center valign=top>
<table border=0 cellspacing=0 cellpadding=0 width=32 height=32 background="Icon.penalty_armor">
<tr>
<td width=32 height=32 align=center valign=top>
<button value=" " action="bypass _bbsstats_attack" width=34 height=34 back="L2UI_CT1.ItemWindow_DF_Frame_Down" fore="L2UI_CT1.ItemWindow_DF_Frame"/>
</td>
</tr>
</table>
Stats
</td>

</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
<table border=0 cellspacing=0 cellpadding=0 width=755>
<tr>
<td height=18></td>
</tr>
<tr>
<td width=755>
<table width=755 height=20>
<tr>
<td valign="top" align="center">
<table border=0 width=755 height=20 cellspacing=4 cellpadding=3 background="l2ui_ct1.ComboBox_DF_Dropmenu_Bg">
<tr>
<td width=755 align=left valign=top>
<table width=745 height=25 bgcolor=0e0d0d>
<tr>
<td width=736 align=center valign=center>
<font color="LEVEL">Server Last Restarted at:</font> %server_uptime%
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td></center>
</tr>
</table>
</body></html>
