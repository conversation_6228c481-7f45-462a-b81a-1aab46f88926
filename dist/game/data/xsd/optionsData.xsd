<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence maxOccurs="1" minOccurs="1">
				<xs:element name="option" maxOccurs="100" minOccurs="1">
					<xs:complexType>
						<xs:sequence maxOccurs="1" minOccurs="1">
							<xs:element name="effects" minOccurs="0" maxOccurs="1">
								<xs:complexType>
									<xs:sequence maxOccurs="unbounded" minOccurs="1">
										<xs:element name="effect" maxOccurs="unbounded" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element type="xs:float" name="amount" minOccurs="0" maxOccurs="1" />
													<xs:element type="xs:string" name="mode" minOccurs="0" maxOccurs="1" />
													<xs:element type="xs:string" name="attribute" minOccurs="0" maxOccurs="1" />
													<xs:element type="xs:byte" name="magicType" minOccurs="0" maxOccurs="1" />
													<xs:element type="xs:string" name="stat" minOccurs="0" maxOccurs="1" />
													<xs:element type="xs:float" name="BOW" minOccurs="0" />
													<xs:element type="xs:float" name="SWORD" minOccurs="0" />
													<xs:element type="xs:float" name="DUAL" minOccurs="0" />
													<xs:element type="xs:float" name="POLE" minOccurs="0" />
													<xs:element type="xs:float" name="FIST" minOccurs="0" />
													<xs:element type="xs:float" name="DUALFIST" minOccurs="0" />
													<xs:element type="xs:float" name="DAGGER" minOccurs="0" />
													<xs:element type="xs:float" name="DUALDAGGER" minOccurs="0" />
													<xs:element type="xs:float" name="ANCIENTSWORD" minOccurs="0" />
													<xs:element type="xs:float" name="RAPIER" minOccurs="0" />
													<xs:element type="xs:float" name="BLUNT" minOccurs="0" />
													<xs:element type="xs:float" name="KNOCKDOWN" minOccurs="0" />
													<xs:element type="xs:float" name="KNOCKBACK" minOccurs="0" />
													<xs:element type="xs:float" name="HOLD" minOccurs="0" />
													<xs:element type="xs:float" name="IMPRISON" minOccurs="0" />
													<xs:element type="xs:float" name="PULL" minOccurs="0" />
													<xs:element type="xs:float" name="PARALYZE" minOccurs="0" />
												</xs:sequence>
												<xs:attribute name="name" type="xs:string" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="active_skill" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="id" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="65535" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="level" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="99" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
							<xs:element name="passive_skill" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="id" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="65535" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="level" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="99" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
							<xs:element name="attack_skill" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="id" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="65535" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="level" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="99" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="chance" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:decimal">
												<xs:minInclusive value="1.0" />
												<xs:maxInclusive value="100.0" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
							<xs:element name="critical_skill" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="id" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="65535" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="level" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="99" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="chance" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:decimal">
												<xs:minInclusive value="1.0" />
												<xs:maxInclusive value="100.0" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
							<xs:element name="magic_skill" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="id" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="65535" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="level" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:positiveInteger">
												<xs:minInclusive value="1" />
												<xs:maxInclusive value="99" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
									<xs:attribute name="chance" use="required">
										<xs:simpleType>
											<xs:restriction base="xs:decimal">
												<xs:minInclusive value="1.0" />
												<xs:maxInclusive value="100.0" />
											</xs:restriction>
										</xs:simpleType>
									</xs:attribute>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
						<xs:attribute name="id" use="required">
							<xs:simpleType>
								<xs:restriction base="xs:positiveInteger">
									<xs:minInclusive value="1" />
								</xs:restriction>
							</xs:simpleType>
						</xs:attribute>
						<xs:attribute type="xs:token" name="name" use="required" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>