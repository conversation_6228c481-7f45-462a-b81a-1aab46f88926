<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="clanHall">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="auction">
								<xs:complexType>
									<xs:attribute type="xs:string" name="minBid" use="required" />
									<xs:attribute type="xs:string" name="lease" use="required" />
									<xs:attribute type="xs:string" name="deposit" use="required" />
								</xs:complexType>
							</xs:element>
							<xs:element name="npcs" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="npc" maxOccurs="unbounded" minOccurs="0">
											<xs:complexType>
												<xs:attribute type="xs:int" name="id" use="optional" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="doorlist" minOccurs="0" maxOccurs="1">
								<xs:complexType>
									<xs:sequence maxOccurs="1">
										<xs:element name="door" maxOccurs="unbounded">
											<xs:complexType>
												<xs:attribute name="id" type="xs:positiveInteger" use="required" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="ownerRestartPoint">
								<xs:complexType>
									<xs:attribute name="x" type="xs:integer" use="required" />
									<xs:attribute name="y" type="xs:integer" use="required" />
									<xs:attribute name="z" type="xs:integer" use="required" />
								</xs:complexType>
							</xs:element>
							<xs:element name="banishPoint">
								<xs:complexType>
									<xs:attribute name="x" type="xs:integer" use="required" />
									<xs:attribute name="y" type="xs:integer" use="required" />
									<xs:attribute name="z" type="xs:integer" use="required" />
								</xs:complexType>
							</xs:element>							
						</xs:sequence>
						<xs:attribute type="xs:short" name="id" use="required" />
						<xs:attribute type="xs:string" name="name" />
						<xs:attribute name="grade">
							<xs:simpleType>
								<xs:restriction base="xs:token">
									<xs:enumeration value="GRADE_NONE" />
									<xs:enumeration value="GRADE_D" />
									<xs:enumeration value="GRADE_C" />
									<xs:enumeration value="GRADE_C" />
									<xs:enumeration value="GRADE_B" />
									<xs:enumeration value="GRADE_A" />
									<xs:enumeration value="GRADE_S" />
								</xs:restriction>
							</xs:simpleType>
						</xs:attribute>
						<xs:attribute name="type">
							<xs:simpleType>
								<xs:restriction base="xs:token">
									<xs:enumeration value="AUCTIONABLE" />
									<xs:enumeration value="SIEGEABLE" />
									<xs:enumeration value="OTHER" />
								</xs:restriction>
							</xs:simpleType>
						</xs:attribute>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>