<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:complexType name="fence">
		<xs:attribute type="xs:string" name="name" />
		<xs:attribute type="xs:integer" name="x" use="required" />
		<xs:attribute type="xs:integer" name="y" use="required" />
		<xs:attribute type="xs:integer" name="z" use="required" />
		<xs:attribute type="xs:positiveInteger" name="width" use="required" />
		<xs:attribute type="xs:positiveInteger" name="length" use="required" />
		<xs:attribute name="height" use="required">
			<xs:simpleType>
				<xs:restriction base="xs:positiveInteger">
					<xs:minInclusive value="1" />
					<xs:maxInclusive value="3" />
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="state" use="required">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:enumeration value="HIDDEN" />
					<xs:enumeration value="OPENED" />
					<xs:enumeration value="CLOSED" />
					<xs:enumeration value="CLOSED_HIDDEN" />
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:complexType>
	
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="fence" maxOccurs="unbounded" type="fence" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>