<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/buylist.xsd">
	<item id="8294" price="0" /> <!-- Recipe: Steel Sword (100%) -->
	<item id="8299" price="0" /> <!-- Recipe: Infernal Master (100%) -->
	<item id="8300" price="0" /> <!-- Recipe: Infernal Master (60%) -->
	<item id="8313" price="0" /> <!-- Recipe: Spiritual Eye (100%) -->
	<item id="8314" price="0" /> <!-- Recipe: Spiritual Eye (60%) -->
	<item id="8315" price="0" /> <!-- Recipe: Flaming Dragon Skull (100%) -->
	<item id="8316" price="0" /> <!-- Recipe: Flaming Dragon Skull (60%) -->
	<item id="8317" price="0" /> <!-- Recipe: Titan Hammer (100%) -->
	<item id="8320" price="0" /> <!-- Recipe: Destroyer Hammer (100%) -->
	<item id="8487" price="0" /> <!-- Recipe: Destroyer Hammer (60%) -->
	<item id="8325" price="0" /> <!-- Recipe: Doom Crusher (100%) -->
	<item id="8326" price="0" /> <!-- Recipe: Doom Crusher (60%) -->
</list>
