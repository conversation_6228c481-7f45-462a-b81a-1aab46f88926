<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="4700" toLevel="13" name="Gift of Queen">
		<!-- Level 1: For 2 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 2: For 4 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 3: For 6 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 4: For 8 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 5: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 6: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 7: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 8: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 9: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 10: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 11: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 12: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<!-- Level 13: For 10 min., party members' P. Atk. +10%, P. Accuracy +3. -->
		<icon>icon.skill1331</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">3</value>
			<value level="7">3</value>
			<value level="8">3</value>
			<value level="9">3</value>
			<value level="10">3</value>
			<value level="11">3</value>
			<value level="12">3</value>
			<value level="13">3</value>
		</abnormalLevel>
		<abnormalTime>
			<value level="1">120</value>
			<value level="2">240</value>
			<value level="3">360</value>
			<value level="4">480</value>
			<value level="5">600</value>
			<value level="6">180</value>
			<value level="7">195</value>
			<value level="8">210</value>
			<value level="9">225</value>
			<value level="10">240</value>
			<value level="11">255</value>
			<value level="12">270</value>
			<value level="13">285</value>
		</abnormalTime>
		<abnormalType>BUFF_QUEEN_OF_CAT2</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>
			<value level="1">541</value>
			<value level="2">611</value>
			<value level="3">659</value>
			<value level="4">666</value>
			<value level="5">669</value>
			<value level="6">673</value>
			<value level="7">676</value>
			<value level="8">679</value>
			<value level="9">681</value>
			<value level="10">684</value>
			<value level="11">687</value>
			<value level="12">689</value>
			<value level="13">692</value>
		</effectPoint>
		<hitTime>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
			<value level="11">4000</value>
			<value level="12">4000</value>
			<value level="13">4000</value>
		</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">77</value>
			<value level="2">79</value>
			<value level="3">81</value>
			<value level="4">83</value>
			<value level="5">84</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">161</value>
			<value level="2">163</value>
			<value level="3">165</value>
			<value level="4">167</value>
			<value level="5">169</value>
			<value level="6">212</value>
			<value level="7">215</value>
			<value level="8">220</value>
			<value level="9">224</value>
			<value level="10">229</value>
			<value level="11">233</value>
			<value level="12">237</value>
			<value level="13">242</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">41</value>
			<value level="2">42</value>
			<value level="3">43</value>
			<value level="4">44</value>
			<value level="5">45</value>
			<value level="6">43</value>
			<value level="7">43</value>
			<value level="8">44</value>
			<value level="9">45</value>
			<value level="10">46</value>
			<value level="11">47</value>
			<value level="12">48</value>
			<value level="13">49</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>3</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4701" toLevel="13" name="Cure of Queen">
		<!-- Level 1: Has a 40% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 2: Has a 50% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 3: Has a 60% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 4: Has a 70% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 5: Has a 80% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 6: Has a 90% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 7: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 8: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 9: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 10: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 11: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 12: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<!-- Level 13: Has a 55% chance to remove curses that decrease P. Accuracy, P./ M. Atk., Atk. Spd./ Casting Spd. and Speed from all party members. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<effectPoint>
			<value level="1">541</value>
			<value level="2">611</value>
			<value level="3">659</value>
			<value level="4">666</value>
			<value level="5">669</value>
			<value level="6">673</value>
			<value level="7">676</value>
			<value level="8">679</value>
			<value level="9">681</value>
			<value level="10">684</value>
			<value level="11">687</value>
			<value level="12">689</value>
			<value level="13">692</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">1000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
		</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">77</value>
			<value level="2">79</value>
			<value level="3">81</value>
			<value level="4">83</value>
			<value level="5">84</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">81</value>
			<value level="2">85</value>
			<value level="3">88</value>
			<value level="4">92</value>
			<value level="5">96</value>
			<value level="6">100</value>
			<value level="7">108</value>
			<value level="8">110</value>
			<value level="9">113</value>
			<value level="10">115</value>
			<value level="11">117</value>
			<value level="12">119</value>
			<value level="13">122</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">21</value>
			<value level="2">22</value>
			<value level="3">22</value>
			<value level="4">23</value>
			<value level="5">24</value>
			<value level="6">25</value>
			<value level="7">43</value>
			<value level="8">44</value>
			<value level="9">45</value>
			<value level="10">46</value>
			<value level="11">47</value>
			<value level="12">48</value>
			<value level="13">49</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="DispelBySlotProbability">
				<dispel>ATTACK_TIME_UP;CASTING_TIME_UP;HIT_DOWN;PA_DOWN;MA_DOWN;SPEED_DOWN;ALL_SPEED_DOWN;WARRIOR_BANE;MAGE_BANE</dispel>
				<rate>
					<value level="1">40</value>
					<value level="2">50</value>
					<value level="3">60</value>
					<value level="4">70</value>
					<value level="5">80</value>
					<value level="6">90</value>
					<value level="7">70</value>
					<value level="8">75</value>
					<value level="9">80</value>
					<value level="10">85</value>
					<value level="11">90</value>
					<value level="12">95</value>
					<value level="13">100</value>
				</rate>
			</effect>
		</effects>
	</skill>
	<skill id="4702" toLevel="13" name="Blessing of Seraphim">
		<!-- Level 1: For 2 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 2: For 4 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 3: For 6 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 4: For 8 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 5: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 6: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 7: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 8: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 9: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 10: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 11: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 12: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<!-- Level 13: For 10 min., party members' MP Recovery Bonus +30%, M. Critical Rate +25%. -->
		<icon>icon.skill1332</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">3</value>
			<value level="7">3</value>
			<value level="8">3</value>
			<value level="9">3</value>
			<value level="10">3</value>
			<value level="11">3</value>
			<value level="12">3</value>
			<value level="13">3</value>
		</abnormalLevel>
		<abnormalTime>
			<value level="1">120</value>
			<value level="2">240</value>
			<value level="3">360</value>
			<value level="4">480</value>
			<value level="5">600</value>
			<value level="6">180</value>
			<value level="7">195</value>
			<value level="8">210</value>
			<value level="9">225</value>
			<value level="10">240</value>
			<value level="11">255</value>
			<value level="12">270</value>
			<value level="13">285</value>
		</abnormalTime>
		<abnormalType>BUFF_UNICORN_SERAPHIM</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>
			<value level="1">541</value>
			<value level="2">611</value>
			<value level="3">659</value>
			<value level="4">666</value>
			<value level="5">669</value>
			<value level="6">673</value>
			<value level="7">676</value>
			<value level="8">679</value>
			<value level="9">681</value>
			<value level="10">684</value>
			<value level="11">687</value>
			<value level="12">689</value>
			<value level="13">692</value>
		</effectPoint>
		<hitTime>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
			<value level="11">4000</value>
			<value level="12">4000</value>
			<value level="13">4000</value>
		</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">77</value>
			<value level="2">79</value>
			<value level="3">81</value>
			<value level="4">83</value>
			<value level="5">84</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">161</value>
			<value level="2">163</value>
			<value level="3">165</value>
			<value level="4">167</value>
			<value level="5">169</value>
			<value level="6">212</value>
			<value level="7">215</value>
			<value level="8">220</value>
			<value level="9">224</value>
			<value level="10">229</value>
			<value level="11">233</value>
			<value level="12">237</value>
			<value level="13">242</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">41</value>
			<value level="2">42</value>
			<value level="3">43</value>
			<value level="4">44</value>
			<value level="5">45</value>
			<value level="6">25</value>
			<value level="7">22</value>
			<value level="8">22</value>
			<value level="9">23</value>
			<value level="10">23</value>
			<value level="11">24</value>
			<value level="12">24</value>
			<value level="13">25</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="MpRegen">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>25</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4703" toLevel="13" name="Gift of Seraphim">
		<!-- Level 1: For 2 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 2: For 4 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 3: For 6 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 4: For 8 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 5: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 6: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 7: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 8: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 9: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 10: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 11: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 12: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<!-- Level 13: For 10 min., party members' M. Skill Cooldown -10%, M. Atk. +30%. -->
		<icon>icon.skill1332</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">3</value>
			<value level="7">3</value>
			<value level="8">3</value>
			<value level="9">3</value>
			<value level="10">3</value>
			<value level="11">3</value>
			<value level="12">3</value>
			<value level="13">3</value>
		</abnormalLevel>
		<abnormalTime>
			<value level="1">120</value>
			<value level="2">240</value>
			<value level="3">360</value>
			<value level="4">480</value>
			<value level="5">600</value>
			<value level="6">180</value>
			<value level="7">195</value>
			<value level="8">210</value>
			<value level="9">225</value>
			<value level="10">240</value>
			<value level="11">255</value>
			<value level="12">270</value>
			<value level="13">285</value>
		</abnormalTime>
		<abnormalType>BUFF_UNICORN_SERAPHIM2</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>
			<value level="1">541</value>
			<value level="2">611</value>
			<value level="3">659</value>
			<value level="4">666</value>
			<value level="5">669</value>
			<value level="6">673</value>
			<value level="7">676</value>
			<value level="8">679</value>
			<value level="9">681</value>
			<value level="10">684</value>
			<value level="11">687</value>
			<value level="12">689</value>
			<value level="13">692</value>
		</effectPoint>
		<hitTime>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">4000</value>
			<value level="7">4000</value>
			<value level="8">4000</value>
			<value level="9">4000</value>
			<value level="10">4000</value>
			<value level="11">4000</value>
			<value level="12">4000</value>
			<value level="13">4000</value>
		</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">77</value>
			<value level="2">79</value>
			<value level="3">81</value>
			<value level="4">83</value>
			<value level="5">84</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">161</value>
			<value level="2">163</value>
			<value level="3">165</value>
			<value level="4">167</value>
			<value level="5">169</value>
			<value level="6">212</value>
			<value level="7">215</value>
			<value level="8">220</value>
			<value level="9">224</value>
			<value level="10">229</value>
			<value level="11">233</value>
			<value level="12">237</value>
			<value level="13">242</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">41</value>
			<value level="2">42</value>
			<value level="3">43</value>
			<value level="4">44</value>
			<value level="5">45</value>
			<value level="6">25</value>
			<value level="7">22</value>
			<value level="8">22</value>
			<value level="9">23</value>
			<value level="10">23</value>
			<value level="11">24</value>
			<value level="12">24</value>
			<value level="13">25</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="Reuse">
				<amount>-10</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MagicalAttack">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4704" toLevel="13" name="Cure of Seraphim">
		<!-- Level 1: Has a 40% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 2: Has a 50% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 3: Has a 60% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 4: Has a 70% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 5: Has a 80% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 6: Has a 90% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 7: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 8: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 9: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 10: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 11: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 12: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<!-- Level 13: Has a 55% chance to remove curses that inflicts Sleep, Hold, Stun, Paralysis and Mental from all party members. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<effectPoint>
			<value level="1">541</value>
			<value level="2">611</value>
			<value level="3">659</value>
			<value level="4">666</value>
			<value level="5">669</value>
			<value level="6">673</value>
			<value level="7">676</value>
			<value level="8">679</value>
			<value level="9">681</value>
			<value level="10">684</value>
			<value level="11">687</value>
			<value level="12">689</value>
			<value level="13">692</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">1000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
		</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">57</value>
			<value level="2">66</value>
			<value level="3">75</value>
			<value level="4">77</value>
			<value level="5">78</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">81</value>
			<value level="2">85</value>
			<value level="3">88</value>
			<value level="4">92</value>
			<value level="5">96</value>
			<value level="6">100</value>
			<value level="7">108</value>
			<value level="8">110</value>
			<value level="9">113</value>
			<value level="10">115</value>
			<value level="11">117</value>
			<value level="12">119</value>
			<value level="13">122</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">21</value>
			<value level="2">22</value>
			<value level="3">22</value>
			<value level="4">23</value>
			<value level="5">24</value>
			<value level="6">25</value>
			<value level="7">22</value>
			<value level="8">22</value>
			<value level="9">23</value>
			<value level="10">23</value>
			<value level="11">24</value>
			<value level="12">24</value>
			<value level="13">25</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="DispelBySlotProbability">
				<dispel>SLEEP;ROOT_MAGICALLY;STUN;PARALYZE;SILENCE;SILENCE_PHYSICAL;SILENCE_ALL;TURN_FLEE</dispel>
				<rate>
					<value level="1">40</value>
					<value level="2">50</value>
					<value level="3">60</value>
					<value level="4">70</value>
					<value level="5">80</value>
					<value level="6">90</value>
					<value level="7">70</value>
					<value level="8">70</value>
					<value level="9">75</value>
					<value level="10">75</value>
					<value level="11">80</value>
					<value level="12">80</value>
					<value level="13">85</value>
				</rate>
			</effect>
		</effects>
	</skill>
	<skill id="4705" toLevel="13" name="Curse of Shade">
		<!-- Level 1: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -4% -->
		<!-- Level 2: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -5% -->
		<!-- Level 3: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -6% -->
		<!-- Level 4: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -8% -->
		<!-- Level 5: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -10% -->
		<!-- Level 6: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 7: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 8: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 9: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 10: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 11: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 12: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<!-- Level 13: Nightshade's debuff magic. Provokes the enemy to attack and then casts a curse. Momentarily P./ M. Def. -3 -->
		<icon>icon.skill1333</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">4</value>
			<value level="7">4</value>
			<value level="8">4</value>
			<value level="9">4</value>
			<value level="10">4</value>
			<value level="11">4</value>
			<value level="12">4</value>
			<value level="13">4</value>
		</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>DEBUFF_NIGHTSHADE</abnormalType>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>
			<value level="1">-1621</value>
			<value level="2">-1831</value>
			<value level="3">-1975</value>
			<value level="4">-1997</value>
			<value level="5">-2007</value>
			<value level="6">-2017</value>
			<value level="7">-2026</value>
			<value level="8">-2035</value>
			<value level="9">-2043</value>
			<value level="10">-2051</value>
			<value level="11">-2059</value>
			<value level="12">-2066</value>
			<value level="13">-2074</value>
		</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
		</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>30</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">77</value>
			<value level="2">79</value>
			<value level="3">81</value>
			<value level="4">83</value>
			<value level="5">84</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">56</value>
			<value level="2">58</value>
			<value level="3">60</value>
			<value level="4">62</value>
			<value level="5">64</value>
			<value level="6">58</value>
			<value level="7">58</value>
			<value level="8">58</value>
			<value level="9">59</value>
			<value level="10">59</value>
			<value level="11">60</value>
			<value level="12">60</value>
			<value level="13">60</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">14</value>
			<value level="2">15</value>
			<value level="3">16</value>
			<value level="4">17</value>
			<value level="5">18</value>
			<value level="6">15</value>
			<value level="7">15</value>
			<value level="8">15</value>
			<value level="9">15</value>
			<value level="10">15</value>
			<value level="11">15</value>
			<value level="12">15</value>
			<value level="13">15</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="TargetMeProbability">
				<chance>80</chance>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">-4</value>
					<value level="2">-5</value>
					<value level="3">-6</value>
					<value level="4">-8</value>
					<value level="5">-10</value>
					<value level="6">-10</value>
					<value level="7">-10</value>
					<value level="8">-10</value>
					<value level="9">-10</value>
					<value level="10">-10</value>
					<value level="11">-10</value>
					<value level="12">-10</value>
					<value level="13">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">-4</value>
					<value level="2">-5</value>
					<value level="3">-6</value>
					<value level="4">-8</value>
					<value level="5">-10</value>
					<value level="6">-10</value>
					<value level="7">-10</value>
					<value level="8">-10</value>
					<value level="9">-10</value>
					<value level="10">-10</value>
					<value level="11">-10</value>
					<value level="12">-10</value>
					<value level="13">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4706" toLevel="13" name="Mass Curse of Shade">
		<!-- Level 1: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -4% -->
		<!-- Level 2: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -5% -->
		<!-- Level 3: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -6% -->
		<!-- Level 4: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -8% -->
		<!-- Level 5: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 6: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 7: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 8: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 9: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 10: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 11: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 12: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<!-- Level 13: Nightshade's debuff magic. Provokes nearby enemies to attack and then casts a curse. Momentarily their P./ M. Def. -10% -->
		<icon>icon.skill1333</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
			<value level="6">4</value>
			<value level="7">4</value>
			<value level="8">4</value>
			<value level="9">4</value>
			<value level="10">4</value>
			<value level="11">4</value>
			<value level="12">4</value>
			<value level="13">4</value>
		</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>DEBUFF_NIGHTSHADE</abnormalType>
		<activateRate>80</activateRate>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>
			<value level="1">-1621</value>
			<value level="2">-1831</value>
			<value level="3">-1975</value>
			<value level="4">-1997</value>
			<value level="5">-2007</value>
			<value level="6">-2017</value>
			<value level="7">-2026</value>
			<value level="8">-2035</value>
			<value level="9">-2043</value>
			<value level="10">-2051</value>
			<value level="11">-2059</value>
			<value level="12">-2066</value>
			<value level="13">-2074</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
		</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>30</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">77</value>
			<value level="2">79</value>
			<value level="3">81</value>
			<value level="4">83</value>
			<value level="5">84</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">64</value>
			<value level="3">68</value>
			<value level="4">72</value>
			<value level="5">76</value>
			<value level="6">86</value>
			<value level="7">87</value>
			<value level="8">87</value>
			<value level="9">88</value>
			<value level="10">89</value>
			<value level="11">89</value>
			<value level="12">90</value>
			<value level="13">90</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">15</value>
			<value level="2">16</value>
			<value level="3">17</value>
			<value level="4">18</value>
			<value level="5">19</value>
			<value level="6">22</value>
			<value level="7">22</value>
			<value level="8">22</value>
			<value level="9">22</value>
			<value level="10">23</value>
			<value level="11">23</value>
			<value level="12">23</value>
			<value level="13">23</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<staticReuse>true</staticReuse>
		<effects>
			<effect name="TargetMeProbability">
				<chance>40</chance>
			</effect>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">-4</value>
					<value level="2">-5</value>
					<value level="3">-6</value>
					<value level="4">-8</value>
					<value level="5">-10</value>
					<value level="6">-10</value>
					<value level="7">-10</value>
					<value level="8">-10</value>
					<value level="9">-10</value>
					<value level="10">-10</value>
					<value level="11">-10</value>
					<value level="12">-10</value>
					<value level="13">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>
					<value level="1">-4</value>
					<value level="2">-5</value>
					<value level="3">-6</value>
					<value level="4">-8</value>
					<value level="5">-10</value>
					<value level="6">-10</value>
					<value level="7">-10</value>
					<value level="8">-10</value>
					<value level="9">-10</value>
					<value level="10">-10</value>
					<value level="11">-10</value>
					<value level="12">-10</value>
					<value level="13">-10</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4707" toLevel="13" name="Shade Sacrifice">
		<!-- Level 1: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 775 -->
		<!-- Level 2: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 800 -->
		<!-- Level 3: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 825 -->
		<!-- Level 4: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 850 -->
		<!-- Level 5: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 875 -->
		<!-- Level 6: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 900 -->
		<!-- Level 7: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 826 -->
		<!-- Level 8: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 835 -->
		<!-- Level 9: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 844 -->
		<!-- Level 10: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 852 -->
		<!-- Level 11: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 860 -->
		<!-- Level 12: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 867 -->
		<!-- Level 13: Nightshade special HP recovery magic. Sacrifices one's own HP to restore HP of nearby allies. Power 874 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<effectPoint>
			<value level="1">250</value>
			<value level="2">260</value>
			<value level="3">270</value>
			<value level="4">280</value>
			<value level="5">290</value>
			<value level="6">300</value>
			<value level="7">826</value>
			<value level="8">835</value>
			<value level="9">844</value>
			<value level="10">852</value>
			<value level="11">860</value>
			<value level="12">867</value>
			<value level="13">874</value>
		</effectPoint>
		<hitTime>
			<value level="1">1000</value>
			<value level="2">1000</value>
			<value level="3">1000</value>
			<value level="4">1000</value>
			<value level="5">1000</value>
			<value level="6">1000</value>
			<value level="7">2000</value>
			<value level="8">2000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
		</hitTime>
		<hpConsume>
			<value level="1">930</value>
			<value level="2">960</value>
			<value level="3">990</value>
			<value level="4">1020</value>
			<value level="5">1050</value>
			<value level="6">1080</value>
			<value level="7">991</value>
			<value level="8">1002</value>
			<value level="9">1013</value>
			<value level="10">1023</value>
			<value level="11">1032</value>
			<value level="12">1041</value>
			<value level="13">1049</value>
		</hpConsume>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">57</value>
			<value level="2">66</value>
			<value level="3">75</value>
			<value level="4">77</value>
			<value level="5">78</value>
			<value level="6">79</value>
			<value level="7">80</value>
			<value level="8">81</value>
			<value level="9">82</value>
			<value level="10">83</value>
			<value level="11">84</value>
			<value level="12">85</value>
			<value level="13">86</value>
		</magicLevel>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">775</value>
					<value level="2">800</value>
					<value level="3">825</value>
					<value level="4">850</value>
					<value level="5">875</value>
					<value level="6">900</value>
					<value level="7">826</value>
					<value level="8">835</value>
					<value level="9">844</value>
					<value level="10">852</value>
					<value level="11">860</value>
					<value level="12">867</value>
					<value level="13">874</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4708" toLevel="14" name="Cursed Strike">
		<!-- Level 1: Cursed Man inflicts damage and stun target simultaneously. Power 1046 -->
		<!-- Level 2: Cursed Man inflicts damage and stun target simultaneously. Power 1230. -->
		<!-- Level 3: Cursed Man inflicts damage and stun target simultaneously. Power 1425. -->
		<!-- Level 4: Cursed Man inflicts damage and stun target simultaneously. Power 1626. -->
		<!-- Level 5: Cursed Man inflicts damage and stun target simultaneously. Power 1727. -->
		<!-- Level 6: Cursed Man inflicts damage and stun target simultaneously. Power 1827. -->
		<!-- Level 7: Cursed Man inflicts damage and stun target simultaneously. Power 1925. -->
		<!-- Level 8: Cursed Man inflicts damage and stun target simultaneously. Power 2020. -->
		<!-- Level 9: Cursed Man inflicts damage and stun target simultaneously. Power 2067 -->
		<!-- Level 10: Cursed Man inflicts damage and stun target simultaneously. Power 2112 -->
		<!-- Level 11: Cursed Man inflicts damage and stun target simultaneously. Power 6459 -->
		<!-- Level 12: Cursed Man inflicts damage and stun target simultaneously. Power 6593. -->
		<!-- Level 13: Cursed Man inflicts damage and stun target simultaneously. Power 7004. -->
		<!-- Level 14: Cursed Man inflicts damage and stun target simultaneously. Power 7443. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-279</value>
			<value level="2">-296</value>
			<value level="3">-311</value>
			<value level="4">-323</value>
			<value level="5">-328</value>
			<value level="6">-333</value>
			<value level="7">-337</value>
			<value level="8">-340</value>
			<value level="9">-341</value>
			<value level="10">-342</value>
			<value level="11">-342</value>
			<value level="12">-342</value>
			<value level="13">-342</value>
			<value level="14">-342</value>
		</effectPoint>
		<effectRange>
			<value level="1">200</value>
			<value level="2">200</value>
			<value level="3">200</value>
			<value level="4">200</value>
			<value level="5">200</value>
			<value level="6">200</value>
			<value level="7">200</value>
			<value level="8">400</value>
			<value level="9">400</value>
			<value level="10">400</value>
			<value level="11">400</value>
			<value level="12">400</value>
			<value level="13">400</value>
			<value level="14">400</value>
		</effectRange>
		<hitTime>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">1000</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
			<value level="14">2000</value>
		</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">58</value>
			<value level="2">62</value>
			<value level="3">66</value>
			<value level="4">70</value>
			<value level="5">72</value>
			<value level="6">74</value>
			<value level="7">76</value>
			<value level="8">78</value>
			<value level="9">79</value>
			<value level="10">80</value>
			<value level="11">85</value>
			<value level="12">90</value>
			<value level="13">95</value>
			<value level="14">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">58</value>
			<value level="2">62</value>
			<value level="3">67</value>
			<value level="4">71</value>
			<value level="5">73</value>
			<value level="6">75</value>
			<value level="7">77</value>
			<value level="8">78</value>
			<value level="9">79</value>
			<value level="10">80</value>
			<value level="11">80</value>
			<value level="12">80</value>
			<value level="13">80</value>
			<value level="14">80</value>
		</mpConsume>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">10000</value>
			<value level="6">10000</value>
			<value level="7">10000</value>
			<value level="8">5000</value>
			<value level="9">5000</value>
			<value level="10">5000</value>
			<value level="11">5000</value>
			<value level="12">5000</value>
			<value level="13">5000</value>
			<value level="14">5000</value>
		</reuseDelay>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1046</value>
					<value level="2">1230</value>
					<value level="3">1425</value>
					<value level="4">1626</value>
					<value level="5">1727</value>
					<value level="6">1827</value>
					<value level="7">1925</value>
					<value level="8">2020</value>
					<value level="9">2067</value>
					<value level="10">2112</value>
					<value level="11">6459</value>
					<value level="12">6593</value>
					<value level="13">7004</value>
					<value level="14">7443</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4709" toLevel="14" name="Cursed Blow">
		<!-- Level 1: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 4181 -->
		<!-- Level 2: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 4920 -->
		<!-- Level 3: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 5700 -->
		<!-- Level 4: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 6503 -->
		<!-- Level 5: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 6906 -->
		<!-- Level 6: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 7305 -->
		<!-- Level 7: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 7698 -->
		<!-- Level 8: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 8079 -->
		<!-- Level 9: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 8265 -->
		<!-- Level 10: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 8447 -->
		<!-- Level 11: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 8743 -->
		<!-- Level 12: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 8918 -->
		<!-- Level 13: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 9101 -->
		<!-- Level 14: Cursed Man's attack that unleashes immense damage upon a targeted enemy. Power 9283 -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<effectPoint>
			<value level="1">-279</value>
			<value level="2">-296</value>
			<value level="3">-311</value>
			<value level="4">-323</value>
			<value level="5">-328</value>
			<value level="6">-333</value>
			<value level="7">-337</value>
			<value level="8">-340</value>
			<value level="9">-341</value>
			<value level="10">-342</value>
			<value level="11">-342</value>
			<value level="12">-342</value>
			<value level="13">-342</value>
			<value level="14">-342</value>
		</effectPoint>
		<effectRange>
			<value level="1">200</value>
			<value level="2">200</value>
			<value level="3">200</value>
			<value level="4">200</value>
			<value level="5">200</value>
			<value level="6">200</value>
			<value level="7">200</value>
			<value level="8">400</value>
			<value level="9">400</value>
			<value level="10">400</value>
			<value level="11">400</value>
			<value level="12">400</value>
			<value level="13">400</value>
			<value level="14">400</value>
		</effectRange>
		<hitTime>
			<value level="1">2000</value>
			<value level="2">2000</value>
			<value level="3">2000</value>
			<value level="4">2000</value>
			<value level="5">2000</value>
			<value level="6">2000</value>
			<value level="7">2000</value>
			<value level="8">1500</value>
			<value level="9">2000</value>
			<value level="10">2000</value>
			<value level="11">2000</value>
			<value level="12">2000</value>
			<value level="13">2000</value>
			<value level="14">2000</value>
		</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">58</value>
			<value level="2">62</value>
			<value level="3">66</value>
			<value level="4">70</value>
			<value level="5">72</value>
			<value level="6">74</value>
			<value level="7">76</value>
			<value level="8">78</value>
			<value level="9">79</value>
			<value level="10">80</value>
			<value level="11">85</value>
			<value level="12">90</value>
			<value level="13">95</value>
			<value level="14">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">58</value>
			<value level="2">62</value>
			<value level="3">67</value>
			<value level="4">71</value>
			<value level="5">73</value>
			<value level="6">75</value>
			<value level="7">77</value>
			<value level="8">78</value>
			<value level="9">79</value>
			<value level="10">80</value>
			<value level="11">80</value>
			<value level="12">80</value>
			<value level="13">80</value>
			<value level="14">80</value>
		</mpConsume>
		<reuseDelay>
			<value level="1">10000</value>
			<value level="2">10000</value>
			<value level="3">10000</value>
			<value level="4">10000</value>
			<value level="5">10000</value>
			<value level="6">10000</value>
			<value level="7">10000</value>
			<value level="8">5000</value>
			<value level="9">5000</value>
			<value level="10">5000</value>
			<value level="11">5000</value>
			<value level="12">5000</value>
			<value level="13">5000</value>
			<value level="14">5000</value>
		</reuseDelay>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">4181</value>
					<value level="2">4920</value>
					<value level="3">5700</value>
					<value level="4">6503</value>
					<value level="5">6906</value>
					<value level="6">7305</value>
					<value level="7">7698</value>
					<value level="8">8079</value>
					<value level="9">8265</value>
					<value level="10">8447</value>
					<value level="11">8743</value>
					<value level="12">8918</value>
					<value level="13">9101</value>
					<value level="14">9283</value>
				</power>
				<criticalChance>10</criticalChance>
				<chanceBoost>200</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4710" toLevel="12" name="Wild Stun">
		<!-- Level 1: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 16 -->
		<!-- Level 2: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 35 -->
		<!-- Level 3: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 73 -->
		<!-- Level 4: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 138 -->
		<!-- Level 5: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 238 -->
		<!-- Level 6: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 375 -->
		<!-- Level 7: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 537 -->
		<!-- Level 8: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 619 -->
		<!-- Level 9: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 697 -->
		<!-- Level 10: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 766 -->
		<!-- Level 11: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 821 -->
		<!-- Level 12: Debuff attack used by Hatchling of Wind/ Strider. Inflicts damage and stun simultaneously. Power 864 -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>15</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>400</coolTime>
		<effectPoint>
			<value level="1">-49</value>
			<value level="2">-73</value>
			<value level="3">-102</value>
			<value level="4">-135</value>
			<value level="5">-170</value>
			<value level="6">-202</value>
			<value level="7">-226</value>
			<value level="8">-235</value>
			<value level="9">-240</value>
			<value level="10">-243</value>
			<value level="11">-246</value>
			<value level="12">-248</value>
		</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>1100</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">3</value>
			<value level="2">5</value>
			<value level="3">7</value>
			<value level="4">9</value>
			<value level="5">12</value>
			<value level="6">14</value>
			<value level="7">17</value>
			<value level="8">18</value>
			<value level="9">18</value>
			<value level="10">19</value>
			<value level="11">20</value>
			<value level="12">20</value>
		</mpConsume>
		<reuseDelay>12000</reuseDelay>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">16</value>
					<value level="2">35</value>
					<value level="3">73</value>
					<value level="4">138</value>
					<value level="5">238</value>
					<value level="6">375</value>
					<value level="7">537</value>
					<value level="8">619</value>
					<value level="9">697</value>
					<value level="10">766</value>
					<value level="11">821</value>
					<value level="12">864</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4711" toLevel="12" name="Wild Defense">
		<!-- Wind Strider's defensive skill to escape from a crisis situation. Increases P. Def. and M. Def. significantly while greatly reducing Atk. Spd. and Speed. -->
		<icon>icon.skill0110</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ULTIMATE_BUFF</abnormalType>
		<activateRate>
			<value level="1">-1</value>
			<value level="2">-1</value>
			<value level="3">-1</value>
			<value level="4">-1</value>
			<value level="5">-1</value>
			<value level="6">-1</value>
			<value level="7">-1</value>
			<value level="8">-1</value>
			<value level="9">-1</value>
			<value level="10">-1</value>
			<value level="11">-1</value>
			<value level="12">15</value>
		</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<coolTime>400</coolTime>
		<effectPoint>
			<value level="1">138</value>
			<value level="2">204</value>
			<value level="3">285</value>
			<value level="4">379</value>
			<value level="5">477</value>
			<value level="6">566</value>
			<value level="7">635</value>
			<value level="8">659</value>
			<value level="9">676</value>
			<value level="10">689</value>
			<value level="11">700</value>
			<value level="12">709</value>
		</effectPoint>
		<hitTime>1100</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">46</value>
			<value level="2">76</value>
			<value level="3">104</value>
			<value level="4">139</value>
			<value level="5">179</value>
			<value level="6">220</value>
			<value level="7">258</value>
			<value level="8">275</value>
			<value level="9">288</value>
			<value level="10">298</value>
			<value level="11">305</value>
			<value level="12">309</value>
		</mpConsume>
		<reuseDelay>1200000</reuseDelay>
		<effects>
			<effect name="BlockMove">
			</effect>
			<effect name="Speed">
				<amount>-90</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>400</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>400</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>-70</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4712" toLevel="12" name="Bright Burst">
		<!-- Level 1: Star Hatchling/ Strider's attack skill. Power 6 -->
		<!-- Level 2: Star Hatchling/ Strider's attack skill. Power 9 -->
		<!-- Level 3: Star Hatchling/ Strider's attack skill. Power 13 -->
		<!-- Level 4: Star Hatchling/ Strider's attack skill. Power 17 -->
		<!-- Level 5: Star Hatchling/ Strider's attack skill. Power 23 -->
		<!-- Level 6: Star Hatchling/ Strider's attack skill. Power 28 -->
		<!-- Level 7: Star Hatchling/ Strider's attack skill. Power 34 -->
		<!-- Level 8: Star Hatchling/ Strider's attack skill. Power 36 -->
		<!-- Level 9: Star Hatchling/ Strider's attack skill. Power 39 -->
		<!-- Level 10: Star Hatchling/ Strider's attack skill. Power 41 -->
		<!-- Level 11: Star Hatchling/ Strider's attack skill. Power 42 -->
		<!-- Level 12: Star Hatchling/ Strider's attack skill. Power 43 -->
		<icon>icon.skill4712</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<coolTime>500</coolTime>
		<effectPoint>
			<value level="1">-138</value>
			<value level="2">-204</value>
			<value level="3">-285</value>
			<value level="4">-379</value>
			<value level="5">-477</value>
			<value level="6">-566</value>
			<value level="7">-635</value>
			<value level="8">-659</value>
			<value level="9">-676</value>
			<value level="10">-689</value>
			<value level="11">-700</value>
			<value level="12">-709</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitCancelTime>1</hitCancelTime>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">6</value>
			<value level="2">6</value>
			<value level="3">7</value>
			<value level="4">7</value>
			<value level="5">8</value>
			<value level="6">8</value>
			<value level="7">9</value>
			<value level="8">9</value>
			<value level="9">10</value>
			<value level="10">10</value>
			<value level="11">11</value>
			<value level="12">11</value>
		</mpConsume>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">6</value>
					<value level="2">9</value>
					<value level="3">13</value>
					<value level="4">17</value>
					<value level="5">23</value>
					<value level="6">28</value>
					<value level="7">34</value>
					<value level="8">36</value>
					<value level="9">39</value>
					<value level="10">41</value>
					<value level="11">42</value>
					<value level="12">43</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4713" toLevel="12" name="Bright Heal">
		<!-- Level 1: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 33 -->
		<!-- Level 2: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 61 -->
		<!-- Level 3: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 98 -->
		<!-- Level 4: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 145 -->
		<!-- Level 5: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 198 -->
		<!-- Level 6: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 251 -->
		<!-- Level 7: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 298 -->
		<!-- Level 8: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 316 -->
		<!-- Level 9: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 331 -->
		<!-- Level 10: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 347 -->
		<!-- Level 11: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 358 -->
		<!-- Level 12: Star Hatchling/ Strider's recovery magic. Regenerates the pet's HP. Power 366 -->
		<icon>icon.skill4713</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<coolTime>1000</coolTime>
		<effectPoint>
			<value level="1">33</value>
			<value level="2">61</value>
			<value level="3">98</value>
			<value level="4">145</value>
			<value level="5">198</value>
			<value level="6">251</value>
			<value level="7">298</value>
			<value level="8">316</value>
			<value level="9">331</value>
			<value level="10">347</value>
			<value level="11">358</value>
			<value level="12">366</value>
		</effectPoint>
		<hitCancelTime>1</hitCancelTime>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">5</value>
			<value level="2">9</value>
			<value level="3">11</value>
			<value level="4">15</value>
			<value level="5">19</value>
			<value level="6">23</value>
			<value level="7">27</value>
			<value level="8">28</value>
			<value level="9">30</value>
			<value level="10">30</value>
			<value level="11">32</value>
			<value level="12">32</value>
		</mpConsume>
		<reuseDelay>12000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">33</value>
					<value level="2">61</value>
					<value level="3">98</value>
					<value level="4">145</value>
					<value level="5">198</value>
					<value level="6">251</value>
					<value level="7">298</value>
					<value level="8">316</value>
					<value level="9">331</value>
					<value level="10">347</value>
					<value level="11">358</value>
					<value level="12">366</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4714" toLevel="1" name="Twilight Dragon">
		<!-- An active dragon with fast moving feet. -->
		<icon>icon.skill1373</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>35</magicLevel>
	</skill>
	<skill id="4715" toLevel="1" name="Wind Dragon">
		<!-- A warrior type dragon that possesses great power. When it is equipped with a weapon, its P. Atk increases greatly. When it is equipped with armor, P. Def. increases greatly and M. Def. also increases. -->
		<icon>icon.skill1373</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>35</magicLevel>
	</skill>
	<skill id="4716" toLevel="1" name="Star Dragon">
		<!-- A mystic type dragon that possesses powerful magic capability. When it is equipped with a weapon, its M. Atk. increases greatly. When equipped with armor, its M. Def. increases greatly and P. Def. also increases. -->
		<icon>icon.skill1373</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>35</magicLevel>
	</skill>
	<skill id="4717" toLevel="12" name="Heal Trick">
		<!-- Level 1: Recovery magic used by a pet. Regenerates some of the master's HP. Power 9 -->
		<!-- Level 2: Recovery magic used by a pet. Regenerates some of the master's HP. Power 16 -->
		<!-- Level 3: Recovery magic used by a pet. Regenerates some of the master's HP. Power 25 -->
		<!-- Level 4: Recovery magic used by a pet. Regenerates some of the master's HP. Power 37 -->
		<!-- Level 5: Recovery magic used by a pet. Regenerates some of the master's HP. Power 50 -->
		<!-- Level 6: Recovery magic used by a pet. Regenerates some of the master's HP. Power 63 -->
		<!-- Level 7: Recovery magic used by a pet. Regenerates some of the master's HP. Power 75 -->
		<!-- Level 8: Recovery magic used by a pet. Regenerates some of the master's HP. Power 79 -->
		<!-- Level 9: Recovery magic used by a pet. Regenerates some of the master's HP. Power 83 -->
		<!-- Level 10: Recovery magic used by a pet. Regenerates some of the master's HP. Power 87 -->
		<!-- Level 11: Recovery magic used by a pet. Regenerates some of the master's HP. Power 90 -->
		<!-- Level 12: Recovery magic used by a pet. Regenerates some of the master's HP. Power 92 -->
		<icon>icon.skill4717</icon>
		<operateType>A1</operateType>
		<targetType>OWNER_PET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">9</value>
			<value level="2">16</value>
			<value level="3">25</value>
			<value level="4">37</value>
			<value level="5">50</value>
			<value level="6">63</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">83</value>
			<value level="10">87</value>
			<value level="11">90</value>
			<value level="12">92</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">5</value>
			<value level="2">8</value>
			<value level="3">12</value>
			<value level="4">17</value>
			<value level="5">20</value>
			<value level="6">25</value>
			<value level="7">29</value>
			<value level="8">32</value>
			<value level="9">33</value>
			<value level="10">33</value>
			<value level="11">34</value>
			<value level="12">34</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">9</value>
					<value level="2">16</value>
					<value level="3">25</value>
					<value level="4">37</value>
					<value level="5">50</value>
					<value level="6">63</value>
					<value level="7">75</value>
					<value level="8">79</value>
					<value level="9">83</value>
					<value level="10">87</value>
					<value level="11">90</value>
					<value level="12">92</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4718" toLevel="12" name="Greater Heal Trick">
		<!-- Level 1: Recovery magic used by a pet. Regenerates its master's HP. Power 37 -->
		<!-- Level 2: Recovery magic used by a pet. Regenerates its master's HP. Power 64 -->
		<!-- Level 3: Recovery magic used by a pet. Regenerates its master's HP. Power 99 -->
		<!-- Level 4: Recovery magic used by a pet. Regenerates its master's HP. Power 139 -->
		<!-- Level 5: Recovery magic used by a pet. Regenerates its master's HP. Power 171 -->
		<!-- Level 6: Recovery magic used by a pet. Regenerates its master's HP. Power 205 -->
		<!-- Level 7: Recovery magic used by a pet. Regenerates its master's HP. Power 236 -->
		<!-- Level 8: Recovery magic used by a pet. Regenerates its master's HP. Power 266 -->
		<!-- Level 9: Recovery magic used by a pet. Regenerates its master's HP. Power 300 -->
		<!-- Level 10: Recovery magic used by a pet. Regenerates its master's HP. Power 332 -->
		<!-- Level 11: Recovery magic used by a pet. Regenerates its master's HP. Power 355 -->
		<!-- Level 12: Recovery magic used by a pet. Regenerates its master's HP. Power 368 -->
		<icon>icon.skill4718</icon>
		<operateType>A1</operateType>
		<targetType>OWNER_PET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">62</value>
			<value level="2">113</value>
			<value level="3">184</value>
			<value level="4">272</value>
			<value level="5">371</value>
			<value level="6">471</value>
			<value level="7">558</value>
			<value level="8">592</value>
			<value level="9">620</value>
			<value level="10">651</value>
			<value level="11">672</value>
			<value level="12">685</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">10</value>
			<value level="2">20</value>
			<value level="3">30</value>
			<value level="4">40</value>
			<value level="5">50</value>
			<value level="6">60</value>
			<value level="7">70</value>
			<value level="8">75</value>
			<value level="9">80</value>
			<value level="10">85</value>
			<value level="11">90</value>
			<value level="12">95</value>
		</magicLevel>
		<mpConsume>
			<value level="1">33</value>
			<value level="2">59</value>
			<value level="3">88</value>
			<value level="4">122</value>
			<value level="5">162</value>
			<value level="6">195</value>
			<value level="7">228</value>
			<value level="8">242</value>
			<value level="9">252</value>
			<value level="10">259</value>
			<value level="11">262</value>
			<value level="12">262</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">62</value>
					<value level="2">113</value>
					<value level="3">184</value>
					<value level="4">272</value>
					<value level="5">371</value>
					<value level="6">471</value>
					<value level="7">558</value>
					<value level="8">592</value>
					<value level="9">620</value>
					<value level="10">651</value>
					<value level="11">672</value>
					<value level="12">685</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4719" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">112</value>
					<value level="2">302</value>
					<value level="3">834</value>
					<value level="4">2177</value>
					<value level="5">5631</value>
					<value level="6">11008</value>
					<value level="7">17044</value>
					<value level="8">20119</value>
					<value level="9">23243</value>
					<value level="10">26363</value>
					<value level="11">29567</value>
					<value level="12">32796</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4720" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">95</value>
					<value level="2">255</value>
					<value level="3">705</value>
					<value level="4">1840</value>
					<value level="5">4754</value>
					<value level="6">9291</value>
					<value level="7">14390</value>
					<value level="8">16978</value>
					<value level="9">19710</value>
					<value level="10">22699</value>
					<value level="11">25871</value>
					<value level="12">28860</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4721" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">51</value>
					<value level="2">136</value>
					<value level="3">375</value>
					<value level="4">982</value>
					<value level="5">2536</value>
					<value level="6">4958</value>
					<value level="7">7667</value>
					<value level="8">9037</value>
					<value level="9">10483</value>
					<value level="10">12074</value>
					<value level="11">13778</value>
					<value level="12">15349</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4722" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">48</value>
					<value level="2">128</value>
					<value level="3">352</value>
					<value level="4">922</value>
					<value level="5">2377</value>
					<value level="6">4651</value>
					<value level="7">7195</value>
					<value level="8">8489</value>
					<value level="9">9855</value>
					<value level="10">11363</value>
					<value level="11">12921</value>
					<value level="12">14430</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4723" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">45</value>
					<value level="2">120</value>
					<value level="3">330</value>
					<value level="4">858</value>
					<value level="5">2218</value>
					<value level="6">4344</value>
					<value level="7">6724</value>
					<value level="8">7942</value>
					<value level="9">9228</value>
					<value level="10">10625</value>
					<value level="11">12093</value>
					<value level="12">13512</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4724" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">56</value>
					<value level="2">151</value>
					<value level="3">417</value>
					<value level="4">1089</value>
					<value level="5">2816</value>
					<value level="6">5504</value>
					<value level="7">8522</value>
					<value level="8">10060</value>
					<value level="9">11622</value>
					<value level="10">13182</value>
					<value level="11">14784</value>
					<value level="12">16398</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4725" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">48</value>
					<value level="2">128</value>
					<value level="3">352</value>
					<value level="4">920</value>
					<value level="5">2377</value>
					<value level="6">4645</value>
					<value level="7">7195</value>
					<value level="8">8490</value>
					<value level="9">9855</value>
					<value level="10">11350</value>
					<value level="11">12936</value>
					<value level="12">14430</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4726" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">26</value>
					<value level="2">68</value>
					<value level="3">188</value>
					<value level="4">491</value>
					<value level="5">1268</value>
					<value level="6">2479</value>
					<value level="7">3834</value>
					<value level="8">4519</value>
					<value level="9">5242</value>
					<value level="10">6037</value>
					<value level="11">6889</value>
					<value level="12">7675</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4727" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">24</value>
					<value level="2">64</value>
					<value level="3">176</value>
					<value level="4">461</value>
					<value level="5">1189</value>
					<value level="6">2326</value>
					<value level="7">3598</value>
					<value level="8">4245</value>
					<value level="9">4928</value>
					<value level="10">5682</value>
					<value level="11">6461</value>
					<value level="12">7215</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4728" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">23</value>
					<value level="2">60</value>
					<value level="3">165</value>
					<value level="4">429</value>
					<value level="5">1109</value>
					<value level="6">2172</value>
					<value level="7">3362</value>
					<value level="8">3971</value>
					<value level="9">4614</value>
					<value level="10">5313</value>
					<value level="11">6047</value>
					<value level="12">6756</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4729" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">168</value>
					<value level="2">453</value>
					<value level="3">1251</value>
					<value level="4">3265</value>
					<value level="5">7446</value>
					<value level="6">16511</value>
					<value level="7">25566</value>
					<value level="8">30178</value>
					<value level="9">34864</value>
					<value level="10">39544</value>
					<value level="11">44350</value>
					<value level="12">49193</value>
				</power>
				<chanceBoost>1150</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4730" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">142</value>
					<value level="2">382</value>
					<value level="3">1056</value>
					<value level="4">2760</value>
					<value level="5">7131</value>
					<value level="6">13935</value>
					<value level="7">21585</value>
					<value level="8">25467</value>
					<value level="9">29565</value>
					<value level="10">34048</value>
					<value level="11">38806</value>
					<value level="12">43290</value>
				</power>
				<chanceBoost>1150</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4731" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">76</value>
					<value level="2">204</value>
					<value level="3">563</value>
					<value level="4">1473</value>
					<value level="5">3804</value>
					<value level="6">7437</value>
					<value level="7">11500</value>
					<value level="8">13555</value>
					<value level="9">15724</value>
					<value level="10">18112</value>
					<value level="11">20667</value>
					<value level="12">23023</value>
				</power>
				<chanceBoost>1150</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4732" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">71</value>
					<value level="2">191</value>
					<value level="3">528</value>
					<value level="4">1382</value>
					<value level="5">3566</value>
					<value level="6">6977</value>
					<value level="7">10793</value>
					<value level="8">12734</value>
					<value level="9">14783</value>
					<value level="10">17044</value>
					<value level="11">19381</value>
					<value level="12">21645</value>
				</power>
				<chanceBoost>1150</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4733" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">67</value>
					<value level="2">179</value>
					<value level="3">494</value>
					<value level="4">1287</value>
					<value level="5">3327</value>
					<value level="6">6516</value>
					<value level="7">10085</value>
					<value level="8">11913</value>
					<value level="9">13841</value>
					<value level="10">15937</value>
					<value level="11">18140</value>
					<value level="12">20268</value>
				</power>
				<chanceBoost>1150</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4734" toLevel="12" name="Spinning Slasher">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">56</value>
					<value level="2">151</value>
					<value level="3">417</value>
					<value level="4">1089</value>
					<value level="5">2816</value>
					<value level="6">5504</value>
					<value level="7">8522</value>
					<value level="8">10060</value>
					<value level="9">11622</value>
					<value level="10">13182</value>
					<value level="11">14784</value>
					<value level="12">16398</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4735" toLevel="12" name="Spinning Slasher">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">48</value>
					<value level="2">128</value>
					<value level="3">352</value>
					<value level="4">920</value>
					<value level="5">2377</value>
					<value level="6">4645</value>
					<value level="7">7195</value>
					<value level="8">8490</value>
					<value level="9">9855</value>
					<value level="10">11350</value>
					<value level="11">12936</value>
					<value level="12">14430</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4736" toLevel="12" name="Spinning Slasher">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">26</value>
					<value level="2">68</value>
					<value level="3">188</value>
					<value level="4">491</value>
					<value level="5">1268</value>
					<value level="6">2479</value>
					<value level="7">3834</value>
					<value level="8">4519</value>
					<value level="9">5242</value>
					<value level="10">6037</value>
					<value level="11">6889</value>
					<value level="12">7675</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4737" toLevel="12" name="Spinning Slasher">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">24</value>
					<value level="2">64</value>
					<value level="3">176</value>
					<value level="4">461</value>
					<value level="5">1189</value>
					<value level="6">2326</value>
					<value level="7">3598</value>
					<value level="8">4245</value>
					<value level="9">4928</value>
					<value level="10">5682</value>
					<value level="11">6461</value>
					<value level="12">7215</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4738" toLevel="12" name="Spinning Slasher">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">23</value>
					<value level="2">60</value>
					<value level="3">165</value>
					<value level="4">429</value>
					<value level="5">1109</value>
					<value level="6">2172</value>
					<value level="7">3362</value>
					<value level="8">3971</value>
					<value level="9">4614</value>
					<value level="10">5313</value>
					<value level="11">6047</value>
					<value level="12">6756</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4739" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">112</value>
					<value level="2">302</value>
					<value level="3">834</value>
					<value level="4">2177</value>
					<value level="5">5631</value>
					<value level="6">11008</value>
					<value level="7">17044</value>
					<value level="8">20119</value>
					<value level="9">23243</value>
					<value level="10">26363</value>
					<value level="11">29567</value>
					<value level="12">32796</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4740" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">95</value>
					<value level="2">255</value>
					<value level="3">705</value>
					<value level="4">1840</value>
					<value level="5">4754</value>
					<value level="6">9291</value>
					<value level="7">14390</value>
					<value level="8">16978</value>
					<value level="9">19710</value>
					<value level="10">22699</value>
					<value level="11">25871</value>
					<value level="12">28860</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4741" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">51</value>
					<value level="2">136</value>
					<value level="3">375</value>
					<value level="4">982</value>
					<value level="5">2536</value>
					<value level="6">4958</value>
					<value level="7">7667</value>
					<value level="8">9037</value>
					<value level="9">10483</value>
					<value level="10">12074</value>
					<value level="11">13778</value>
					<value level="12">15349</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4742" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">48</value>
					<value level="2">128</value>
					<value level="3">352</value>
					<value level="4">922</value>
					<value level="5">2377</value>
					<value level="6">4651</value>
					<value level="7">7195</value>
					<value level="8">8489</value>
					<value level="9">9855</value>
					<value level="10">11363</value>
					<value level="11">12921</value>
					<value level="12">14430</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4743" toLevel="12" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">45</value>
					<value level="2">120</value>
					<value level="3">330</value>
					<value level="4">858</value>
					<value level="5">2218</value>
					<value level="6">4344</value>
					<value level="7">6724</value>
					<value level="8">7942</value>
					<value level="9">9228</value>
					<value level="10">10625</value>
					<value level="11">12093</value>
					<value level="12">13512</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4744" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">56</value>
					<value level="2">151</value>
					<value level="3">417</value>
					<value level="4">1089</value>
					<value level="5">2816</value>
					<value level="6">5504</value>
					<value level="7">8522</value>
					<value level="8">10060</value>
					<value level="9">11622</value>
					<value level="10">13182</value>
					<value level="11">14784</value>
					<value level="12">16398</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4745" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">48</value>
					<value level="2">128</value>
					<value level="3">352</value>
					<value level="4">920</value>
					<value level="5">2377</value>
					<value level="6">4645</value>
					<value level="7">7195</value>
					<value level="8">8490</value>
					<value level="9">9855</value>
					<value level="10">11350</value>
					<value level="11">12936</value>
					<value level="12">14430</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4746" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">26</value>
					<value level="2">68</value>
					<value level="3">188</value>
					<value level="4">491</value>
					<value level="5">1268</value>
					<value level="6">2479</value>
					<value level="7">3834</value>
					<value level="8">4519</value>
					<value level="9">5242</value>
					<value level="10">6037</value>
					<value level="11">6889</value>
					<value level="12">7675</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4747" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">24</value>
					<value level="2">64</value>
					<value level="3">176</value>
					<value level="4">461</value>
					<value level="5">1189</value>
					<value level="6">2326</value>
					<value level="7">3598</value>
					<value level="8">4245</value>
					<value level="9">4928</value>
					<value level="10">5682</value>
					<value level="11">6461</value>
					<value level="12">7215</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4748" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">23</value>
					<value level="2">60</value>
					<value level="3">165</value>
					<value level="4">429</value>
					<value level="5">1109</value>
					<value level="6">2172</value>
					<value level="7">3362</value>
					<value level="8">3971</value>
					<value level="9">4614</value>
					<value level="10">5313</value>
					<value level="11">6047</value>
					<value level="12">6756</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4749" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">280</value>
					<value level="2">755</value>
					<value level="3">2084</value>
					<value level="4">5441</value>
					<value level="5">14076</value>
					<value level="6">27518</value>
					<value level="7">42610</value>
					<value level="8">50296</value>
					<value level="9">58107</value>
					<value level="10">65907</value>
					<value level="11">73917</value>
					<value level="12">81989</value>
				</power>
				<chanceBoost>670</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4750" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">236</value>
					<value level="2">637</value>
					<value level="3">1761</value>
					<value level="4">4600</value>
					<value level="5">11884</value>
					<value level="6">23226</value>
					<value level="7">35974</value>
					<value level="8">42445</value>
					<value level="9">49275</value>
					<value level="10">56746</value>
					<value level="11">64677</value>
					<value level="12">72150</value>
				</power>
				<chanceBoost>670</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4751" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">126</value>
					<value level="2">339</value>
					<value level="3">937</value>
					<value level="4">2455</value>
					<value level="5">6339</value>
					<value level="6">12395</value>
					<value level="7">19166</value>
					<value level="8">22591</value>
					<value level="9">26206</value>
					<value level="10">30185</value>
					<value level="11">34445</value>
					<value level="12">38371</value>
				</power>
				<chanceBoost>670</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4752" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">118</value>
					<value level="2">319</value>
					<value level="3">879</value>
					<value level="4">2303</value>
					<value level="5">5942</value>
					<value level="6">11627</value>
					<value level="7">17987</value>
					<value level="8">21223</value>
					<value level="9">24638</value>
					<value level="10">28406</value>
					<value level="11">32302</value>
					<value level="12">36075</value>
				</power>
				<chanceBoost>670</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4753" toLevel="12" name="Mortal Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">16</value>
			<value level="2">23</value>
			<value level="3">32</value>
			<value level="4">42</value>
			<value level="5">53</value>
			<value level="6">65</value>
			<value level="7">75</value>
			<value level="8">79</value>
			<value level="9">82</value>
			<value level="10">84</value>
			<value level="11">85</value>
			<value level="12">87</value>
		</mpConsume>
		<effects>
			<effect name="FatalBlow">
				<power>
					<value level="1">111</value>
					<value level="2">298</value>
					<value level="3">824</value>
					<value level="4">2145</value>
					<value level="5">5545</value>
					<value level="6">10860</value>
					<value level="7">16808</value>
					<value level="8">19855</value>
					<value level="9">23069</value>
					<value level="10">26561</value>
					<value level="11">30232</value>
					<value level="12">33780</value>
				</power>
				<chanceBoost>670</chanceBoost>
			</effect>
		</effects>
	</skill>
	<skill id="4754" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">327</value>
					<value level="2">882</value>
					<value level="3">2435</value>
					<value level="4">6357</value>
					<value level="5">16445</value>
					<value level="6">32150</value>
					<value level="7">49783</value>
					<value level="8">58763</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4755" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">275</value>
					<value level="2">744</value>
					<value level="3">2057</value>
					<value level="4">5374</value>
					<value level="5">13885</value>
					<value level="6">27135</value>
					<value level="7">42030</value>
					<value level="8">49590</value>
					<value level="9">57569</value>
					<value level="10">66298</value>
					<value level="11">75565</value>
					<value level="12">84295</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4756" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">147</value>
					<value level="2">396</value>
					<value level="3">1095</value>
					<value level="4">2868</value>
					<value level="5">7407</value>
					<value level="6">14481</value>
					<value level="7">22392</value>
					<value level="8">26393</value>
					<value level="9">30618</value>
					<value level="10">35267</value>
					<value level="11">40244</value>
					<value level="12">44830</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4757" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">138</value>
					<value level="2">372</value>
					<value level="3">1027</value>
					<value level="4">2691</value>
					<value level="5">6943</value>
					<value level="6">13584</value>
					<value level="7">21015</value>
					<value level="8">24795</value>
					<value level="9">28785</value>
					<value level="10">33188</value>
					<value level="11">37739</value>
					<value level="12">42148</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4758" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">129</value>
					<value level="2">348</value>
					<value level="3">962</value>
					<value level="4">2506</value>
					<value level="5">6479</value>
					<value level="6">12688</value>
					<value level="7">19638</value>
					<value level="8">23198</value>
					<value level="9">26952</value>
					<value level="10">31032</value>
					<value level="11">35321</value>
					<value level="12">39466</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4759" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">164</value>
					<value level="2">441</value>
					<value level="3">1218</value>
					<value level="4">3179</value>
					<value level="5">8223</value>
					<value level="6">16075</value>
					<value level="7">24892</value>
					<value level="8">29382</value>
					<value level="9">33944</value>
					<value level="10">38501</value>
					<value level="11">43180</value>
					<value level="12">47895</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4760" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">138</value>
					<value level="2">372</value>
					<value level="3">1029</value>
					<value level="4">2687</value>
					<value level="5">6943</value>
					<value level="6">13568</value>
					<value level="7">21015</value>
					<value level="8">24795</value>
					<value level="9">28785</value>
					<value level="10">33149</value>
					<value level="11">37783</value>
					<value level="12">42148</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4761" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">74</value>
					<value level="2">198</value>
					<value level="3">548</value>
					<value level="4">1434</value>
					<value level="5">3704</value>
					<value level="6">7241</value>
					<value level="7">11196</value>
					<value level="8">13197</value>
					<value level="9">15309</value>
					<value level="10">17634</value>
					<value level="11">20122</value>
					<value level="12">22415</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4762" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">69</value>
					<value level="2">186</value>
					<value level="3">514</value>
					<value level="4">1346</value>
					<value level="5">3472</value>
					<value level="6">6792</value>
					<value level="7">10508</value>
					<value level="8">12398</value>
					<value level="9">14393</value>
					<value level="10">16594</value>
					<value level="11">18870</value>
					<value level="12">21074</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4763" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">65</value>
					<value level="2">174</value>
					<value level="3">481</value>
					<value level="4">1253</value>
					<value level="5">3240</value>
					<value level="6">6344</value>
					<value level="7">9819</value>
					<value level="8">11599</value>
					<value level="9">13476</value>
					<value level="10">15516</value>
					<value level="11">17661</value>
					<value level="12">19733</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4764" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">327</value>
					<value level="2">882</value>
					<value level="3">2435</value>
					<value level="4">6357</value>
					<value level="5">16445</value>
					<value level="6">32150</value>
					<value level="7">49783</value>
					<value level="8">58763</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4765" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">275</value>
					<value level="2">744</value>
					<value level="3">2057</value>
					<value level="4">5374</value>
					<value level="5">13885</value>
					<value level="6">27135</value>
					<value level="7">42030</value>
					<value level="8">49590</value>
					<value level="9">57569</value>
					<value level="10">66298</value>
					<value level="11">75565</value>
					<value level="12">84295</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4766" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">147</value>
					<value level="2">396</value>
					<value level="3">1095</value>
					<value level="4">2868</value>
					<value level="5">7407</value>
					<value level="6">14481</value>
					<value level="7">22392</value>
					<value level="8">26393</value>
					<value level="9">30618</value>
					<value level="10">35267</value>
					<value level="11">40244</value>
					<value level="12">44830</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4767" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">138</value>
					<value level="2">372</value>
					<value level="3">1027</value>
					<value level="4">2691</value>
					<value level="5">6943</value>
					<value level="6">13584</value>
					<value level="7">21015</value>
					<value level="8">24795</value>
					<value level="9">28785</value>
					<value level="10">33188</value>
					<value level="11">37739</value>
					<value level="12">42148</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4768" toLevel="12" name="Power Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">129</value>
					<value level="2">348</value>
					<value level="3">962</value>
					<value level="4">2506</value>
					<value level="5">6479</value>
					<value level="6">12688</value>
					<value level="7">19638</value>
					<value level="8">23198</value>
					<value level="9">26952</value>
					<value level="10">31032</value>
					<value level="11">35321</value>
					<value level="12">39466</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4769" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">164</value>
					<value level="2">441</value>
					<value level="3">1218</value>
					<value level="4">3179</value>
					<value level="5">8223</value>
					<value level="6">16075</value>
					<value level="7">24892</value>
					<value level="8">29382</value>
					<value level="9">33944</value>
					<value level="10">38501</value>
					<value level="11">43180</value>
					<value level="12">47895</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4770" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">138</value>
					<value level="2">372</value>
					<value level="3">1029</value>
					<value level="4">2687</value>
					<value level="5">6943</value>
					<value level="6">13568</value>
					<value level="7">21015</value>
					<value level="8">24795</value>
					<value level="9">28785</value>
					<value level="10">33149</value>
					<value level="11">37783</value>
					<value level="12">42148</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4771" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">74</value>
					<value level="2">198</value>
					<value level="3">548</value>
					<value level="4">1434</value>
					<value level="5">3704</value>
					<value level="6">7241</value>
					<value level="7">11196</value>
					<value level="8">13197</value>
					<value level="9">15309</value>
					<value level="10">17634</value>
					<value level="11">20122</value>
					<value level="12">22415</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4772" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">69</value>
					<value level="2">186</value>
					<value level="3">514</value>
					<value level="4">1346</value>
					<value level="5">3472</value>
					<value level="6">6792</value>
					<value level="7">10508</value>
					<value level="8">12398</value>
					<value level="9">14393</value>
					<value level="10">16594</value>
					<value level="11">18870</value>
					<value level="12">21074</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4773" toLevel="12" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>
			<value level="1">5</value>
			<value level="2">5</value>
			<value level="3">5</value>
			<value level="4">5</value>
			<value level="5">5</value>
			<value level="6">5</value>
			<value level="7">5</value>
			<value level="8">5</value>
			<value level="9">5</value>
			<value level="10">9</value>
			<value level="11">9</value>
			<value level="12">9</value>
		</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">65</value>
					<value level="2">174</value>
					<value level="3">481</value>
					<value level="4">1253</value>
					<value level="5">3240</value>
					<value level="6">6344</value>
					<value level="7">9819</value>
					<value level="8">11599</value>
					<value level="9">13476</value>
					<value level="10">15516</value>
					<value level="11">17661</value>
					<value level="12">19733</value>
				</power>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="4774" toLevel="12" name="Spear Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">352</value>
					<value level="2">949</value>
					<value level="3">2607</value>
					<value level="4">6828</value>
					<value level="5">17184</value>
					<value level="6">33213</value>
					<value level="7">51007</value>
					<value level="8">59177</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4775" toLevel="12" name="Spear Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">352</value>
					<value level="2">949</value>
					<value level="3">2607</value>
					<value level="4">6828</value>
					<value level="5">17184</value>
					<value level="6">33213</value>
					<value level="7">51007</value>
					<value level="8">59177</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4776" toLevel="12" name="Spear Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">352</value>
					<value level="2">949</value>
					<value level="3">2607</value>
					<value level="4">6828</value>
					<value level="5">17184</value>
					<value level="6">33213</value>
					<value level="7">51007</value>
					<value level="8">59177</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4777" toLevel="12" name="Spear Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">352</value>
					<value level="2">949</value>
					<value level="3">2607</value>
					<value level="4">6828</value>
					<value level="5">17184</value>
					<value level="6">33213</value>
					<value level="7">51007</value>
					<value level="8">59177</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4778" toLevel="12" name="Spear Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">32</value>
			<value level="2">45</value>
			<value level="3">63</value>
			<value level="4">83</value>
			<value level="5">106</value>
			<value level="6">129</value>
			<value level="7">150</value>
			<value level="8">158</value>
			<value level="9">164</value>
			<value level="10">168</value>
			<value level="11">170</value>
			<value level="12">173</value>
		</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">352</value>
					<value level="2">949</value>
					<value level="3">2607</value>
					<value level="4">6828</value>
					<value level="5">17184</value>
					<value level="6">33213</value>
					<value level="7">51007</value>
					<value level="8">59177</value>
					<value level="9">67888</value>
					<value level="10">77001</value>
					<value level="11">86359</value>
					<value level="12">95790</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4779" toLevel="12" name="Heal">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">34</value>
			<value level="3">50</value>
			<value level="4">67</value>
			<value level="5">86</value>
			<value level="6">104</value>
			<value level="7">119</value>
			<value level="8">125</value>
			<value level="9">129</value>
			<value level="10">131</value>
			<value level="11">131</value>
			<value level="12">132</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">1640</value>
					<value level="2">2824</value>
					<value level="3">4501</value>
					<value level="4">6612</value>
					<value level="5">9334</value>
					<value level="6">11649</value>
					<value level="7">12865</value>
					<value level="8">13406</value>
					<value level="9">13624</value>
					<value level="10">13544</value>
					<value level="11">13303</value>
					<value level="12">13157</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4780" toLevel="12" name="Heal">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">34</value>
			<value level="3">50</value>
			<value level="4">67</value>
			<value level="5">86</value>
			<value level="6">104</value>
			<value level="7">119</value>
			<value level="8">125</value>
			<value level="9">129</value>
			<value level="10">131</value>
			<value level="11">131</value>
			<value level="12">132</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">1382</value>
					<value level="2">2381</value>
					<value level="3">3802</value>
					<value level="4">5590</value>
					<value level="5">7881</value>
					<value level="6">9832</value>
					<value level="7">10862</value>
					<value level="8">11314</value>
					<value level="9">11553</value>
					<value level="10">11661</value>
					<value level="11">11640</value>
					<value level="12">11578</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4781" toLevel="12" name="Heal">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">34</value>
			<value level="3">50</value>
			<value level="4">67</value>
			<value level="5">86</value>
			<value level="6">104</value>
			<value level="7">119</value>
			<value level="8">125</value>
			<value level="9">129</value>
			<value level="10">131</value>
			<value level="11">131</value>
			<value level="12">132</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">736</value>
					<value level="2">1267</value>
					<value level="3">2024</value>
					<value level="4">2983</value>
					<value level="5">4204</value>
					<value level="6">5247</value>
					<value level="7">5787</value>
					<value level="8">6022</value>
					<value level="9">6144</value>
					<value level="10">6203</value>
					<value level="11">6199</value>
					<value level="12">6158</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4782" toLevel="12" name="Heal">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">34</value>
			<value level="3">50</value>
			<value level="4">67</value>
			<value level="5">86</value>
			<value level="6">104</value>
			<value level="7">119</value>
			<value level="8">125</value>
			<value level="9">129</value>
			<value level="10">131</value>
			<value level="11">131</value>
			<value level="12">132</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">691</value>
					<value level="2">1191</value>
					<value level="3">1899</value>
					<value level="4">2799</value>
					<value level="5">3941</value>
					<value level="6">4922</value>
					<value level="7">5431</value>
					<value level="8">5657</value>
					<value level="9">5777</value>
					<value level="10">5838</value>
					<value level="11">5813</value>
					<value level="12">5789</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4783" toLevel="12" name="Heal">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">34</value>
			<value level="3">50</value>
			<value level="4">67</value>
			<value level="5">86</value>
			<value level="6">104</value>
			<value level="7">119</value>
			<value level="8">125</value>
			<value level="9">129</value>
			<value level="10">131</value>
			<value level="11">131</value>
			<value level="12">132</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">647</value>
					<value level="2">1115</value>
					<value level="3">1778</value>
					<value level="4">2607</value>
					<value level="5">3677</value>
					<value level="6">4597</value>
					<value level="7">5075</value>
					<value level="8">5293</value>
					<value level="9">5409</value>
					<value level="10">5458</value>
					<value level="11">5441</value>
					<value level="12">5421</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="4784" toLevel="12" name="Chant of Life">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>
			<value level="1">11</value>
			<value level="2">12</value>
			<value level="3">13</value>
			<value level="4">14</value>
			<value level="5">15</value>
			<value level="6">16</value>
			<value level="7">17</value>
			<value level="8">17</value>
			<value level="9">18</value>
			<value level="10">18</value>
			<value level="11">19</value>
			<value level="12">19</value>
		</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">43</value>
			<value level="2">69</value>
			<value level="3">99</value>
			<value level="4">134</value>
			<value level="5">173</value>
			<value level="6">207</value>
			<value level="7">239</value>
			<value level="8">250</value>
			<value level="9">258</value>
			<value level="10">262</value>
			<value level="11">262</value>
			<value level="12">263</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="HealOverTime">
				<power>
					<value level="1">167</value>
					<value level="2">210</value>
					<value level="3">315</value>
					<value level="4">507</value>
					<value level="5">784</value>
					<value level="6">1026</value>
					<value level="7">1195</value>
					<value level="8">1283</value>
					<value level="9">1358</value>
					<value level="10">1427</value>
					<value level="11">951</value>
					<value level="12">940</value>
				</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="4785" toLevel="12" name="Chant of Life">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>
			<value level="1">11</value>
			<value level="2">12</value>
			<value level="3">13</value>
			<value level="4">14</value>
			<value level="5">15</value>
			<value level="6">16</value>
			<value level="7">17</value>
			<value level="8">17</value>
			<value level="9">18</value>
			<value level="10">18</value>
			<value level="11">19</value>
			<value level="12">19</value>
		</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">43</value>
			<value level="2">69</value>
			<value level="3">99</value>
			<value level="4">134</value>
			<value level="5">173</value>
			<value level="6">207</value>
			<value level="7">239</value>
			<value level="8">250</value>
			<value level="9">258</value>
			<value level="10">262</value>
			<value level="11">262</value>
			<value level="12">263</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="HealOverTime">
				<power>
					<value level="1">141</value>
					<value level="2">177</value>
					<value level="3">266</value>
					<value level="4">428</value>
					<value level="5">662</value>
					<value level="6">866</value>
					<value level="7">1009</value>
					<value level="8">1083</value>
					<value level="9">1152</value>
					<value level="10">1229</value>
					<value level="11">832</value>
					<value level="12">827</value>
				</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="4786" toLevel="12" name="Chant of Life">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>
			<value level="1">11</value>
			<value level="2">12</value>
			<value level="3">13</value>
			<value level="4">14</value>
			<value level="5">15</value>
			<value level="6">16</value>
			<value level="7">17</value>
			<value level="8">17</value>
			<value level="9">18</value>
			<value level="10">18</value>
			<value level="11">19</value>
			<value level="12">19</value>
		</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">43</value>
			<value level="2">69</value>
			<value level="3">99</value>
			<value level="4">134</value>
			<value level="5">173</value>
			<value level="6">207</value>
			<value level="7">239</value>
			<value level="8">250</value>
			<value level="9">258</value>
			<value level="10">262</value>
			<value level="11">262</value>
			<value level="12">263</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="HealOverTime">
				<power>
					<value level="1">75</value>
					<value level="2">94</value>
					<value level="3">142</value>
					<value level="4">229</value>
					<value level="5">353</value>
					<value level="6">463</value>
					<value level="7">538</value>
					<value level="8">576</value>
					<value level="9">613</value>
					<value level="10">654</value>
					<value level="11">443</value>
					<value level="12">440</value>
				</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="4787" toLevel="12" name="Chant of Life">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>
			<value level="1">11</value>
			<value level="2">12</value>
			<value level="3">13</value>
			<value level="4">14</value>
			<value level="5">15</value>
			<value level="6">16</value>
			<value level="7">17</value>
			<value level="8">17</value>
			<value level="9">18</value>
			<value level="10">18</value>
			<value level="11">19</value>
			<value level="12">19</value>
		</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">43</value>
			<value level="2">69</value>
			<value level="3">99</value>
			<value level="4">134</value>
			<value level="5">173</value>
			<value level="6">207</value>
			<value level="7">239</value>
			<value level="8">250</value>
			<value level="9">258</value>
			<value level="10">262</value>
			<value level="11">262</value>
			<value level="12">263</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="HealOverTime">
				<power>
					<value level="1">71</value>
					<value level="2">89</value>
					<value level="3">133</value>
					<value level="4">215</value>
					<value level="5">331</value>
					<value level="6">434</value>
					<value level="7">505</value>
					<value level="8">542</value>
					<value level="9">576</value>
					<value level="10">615</value>
					<value level="11">416</value>
					<value level="12">414</value>
				</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="4788" toLevel="12" name="Chant of Life">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>
			<value level="1">11</value>
			<value level="2">12</value>
			<value level="3">13</value>
			<value level="4">14</value>
			<value level="5">15</value>
			<value level="6">16</value>
			<value level="7">17</value>
			<value level="8">17</value>
			<value level="9">18</value>
			<value level="10">18</value>
			<value level="11">19</value>
			<value level="12">19</value>
		</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>700</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">14</value>
			<value level="2">24</value>
			<value level="3">34</value>
			<value level="4">44</value>
			<value level="5">54</value>
			<value level="6">64</value>
			<value level="7">74</value>
			<value level="8">79</value>
			<value level="9">84</value>
			<value level="10">89</value>
			<value level="11">94</value>
			<value level="12">99</value>
		</magicLevel>
		<mpConsume>
			<value level="1">43</value>
			<value level="2">69</value>
			<value level="3">99</value>
			<value level="4">134</value>
			<value level="5">173</value>
			<value level="6">207</value>
			<value level="7">239</value>
			<value level="8">250</value>
			<value level="9">258</value>
			<value level="10">262</value>
			<value level="11">262</value>
			<value level="12">263</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="HealOverTime">
				<power>
					<value level="1">66</value>
					<value level="2">83</value>
					<value level="3">125</value>
					<value level="4">200</value>
					<value level="5">309</value>
					<value level="6">405</value>
					<value level="7">472</value>
					<value level="8">507</value>
					<value level="9">539</value>
					<value level="10">576</value>
					<value level="11">389</value>
					<value level="12">388</value>
				</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="4789" toLevel="10" name="High Lv.">
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
		<effects>
			<effect name="MagicalDefence">
				<amount>
					<value level="1">3</value>
					<value level="2">6</value>
					<value level="3">9</value>
					<value level="4">12</value>
					<value level="5">15</value>
					<value level="6">18</value>
					<value level="7">21</value>
					<value level="8">24</value>
					<value level="9">27</value>
					<value level="10">30</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>
					<value level="1">1</value>
					<value level="2">2</value>
					<value level="3">3</value>
					<value level="4">4</value>
					<value level="5">5</value>
					<value level="6">6</value>
					<value level="7">7</value>
					<value level="8">8</value>
					<value level="9">9</value>
					<value level="10">10</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="4790" toLevel="1" name="Raid Boss (Lv. 23)">
		<!-- A soldier of the Convict Mercenaries, who were famous for their bravery during the Aden-Gracia War. Currently, he is the Commander of the Gracian Convict Mercenaries, the surviving remnants of the company left in Aden Kingdom. His soldiers are based in Gludio. He waits for an opportunity to return to his country. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4791" toLevel="1" name="Raid Boss (Lv. 30)">
		<!-- The commander of the Katu Mercenaries. Prone to violent outbursts at any cause, he takes special joy in battling both elves and dark elves. He has no care for personal appearance or common courtesies, and some claim him touched by madness. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4792" toLevel="1" name="Raid Boss (Lv. 42)">
		<!-- A wicked Spider that once lived in the Wastelands while Beleth was free. After Beleth was sealed away, the Spider was chased away by a colony of Ants, empowered by M. Atk. of the Wastelands. The Spider was forced to retreat to this place. It swears that someday it will feed on the Ants and destroy them. It quietly grows in power while sharpening its fangs for vengeance. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4793" toLevel="1" name="Raid Boss (Lv. 55)">
		<!-- A Treant who used to live south of Gludio, he loved trees and nature. After the war with Beleth, his beautiful forest home was destroyed, and his friends were slain. Deeply affected by that traumatic loss he is now trapped in a haze of hatred, madness, and the desire for revenge. Gripped by his anger, he is still at war against all humans and mystics. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4794" toLevel="1" name="Raid Boss (Lv. 50)">
		<!-- One of the leaders of the peasants who resisted the merciless rule of Lord Ashton. He was ultimately captured and brutally executed by Ashton's cruel soldiers. Yet even that did not stop him. To overthrow Ashton, he made a contract with a devil and came back to life. But the devil's bargain turned his once noble ideals towards loathing, turning him into a monster with the power of evil magic. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4795" toLevel="1" name="Raid Boss (Lv. 60)">
		<!-- A mechanical golem owned by the Lord of Innadril Castle. The lord's family has been protected by Eva for generations. This golem was created to protect the Garden of Eva, made by a dwarven master artisan. It is a guard to fend off the Water Dragon Fafurion's troops, or invaders threatening the garden. The dwarven master made two other golems, but this third version was the strongest and became the last one remaining. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4796" toLevel="1" name="Raid Boss (Lv. 25)">
		<!-- A Pan who was once a long-time friend of the Elves, and guarded the ancient Elven Forest. The Pans stayed neutral during the tragic war between the Elves and the Dark Elves, and both sides turned against them. The other Pans retreated into hiding after suffering cruel attacks from both sides, but Pay Dryad still remains here waiting for the golden age of peace to return. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4797" toLevel="1" name="Raid Boss (Lv. 34)">
		<!-- An Orc Dark Mage with superior talent. He was once known as "the Hope of the Breka Orc tribe," but he was tricked into plotting against his leader, and exiled. Now, he dreams of the day he will take vengeance on those who betrayed and exiled him. He grows in power, and uses summoning magic learned from the Dark Elves to harness powerful servitors and minions. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4798" toLevel="1" name="Raid Boss (Lv. 34)">
		<!-- She is the Ruthless Queen of Marsh Stakatos, deep in the Cruma Marshlands. The Queen is notorious for mercilessness and cruelty to both her own forces and enemies alike. She has ordered the deaths of any and all who oppose her, including subordinates and even her own children. The Queen yearns to build a kingdom for Marsh Stakatos someday. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
	<skill id="4799" toLevel="1" name="Raid Boss (Lv. 49)">
		<!-- He is the Commander of the advance forces of the powerful Ketra Orc tribe, who hail from the Goddard Territory of Elmore Kingdom. He believes completely in the superiority of his tribe. He was dispatched to expand their influence upon Aden Kingdom. He greatly detests the Silenos as well as humans, and is a formidable enemy. -->
		<icon>icon.skillraid</icon>
		<operateType>P</operateType>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>1</magicLevel>
	</skill>
</list>
