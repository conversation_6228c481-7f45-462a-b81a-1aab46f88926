<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="14000" toLevel="1" name="<PERSON>rserk">
		<!-- P. Atk. is greatly increased because of rage. -->
		<icon>icon.skill1062</icon>
		<operateType>A1</operateType>
		<effectPoint>100</effectPoint>
		<reuseDelay>6000</reuseDelay>
	</skill>
	<skill id="14001" toLevel="3" name="Cross Slash">
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>150</castRange>
		<effectPoint>-354</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">95</value>
			<value level="2">96</value>
			<value level="3">97</value>
		</magicLevel>
		<mpConsume>
			<value level="1">85</value>
			<value level="2">86</value>
			<value level="3">86</value>
		</mpConsume>
		<reuseDelay>2500</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">132687</value>
					<value level="2">138779</value>
					<value level="3">64068</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="14002" toLevel="3" name="Transfixion Attack">
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0484</icon>
		<operateType>DA1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>400</castRange>
		<effectPoint>
			<value level="1">-352</value>
			<value level="2">-353</value>
			<value level="3">-353</value>
		</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>100</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">95</value>
			<value level="2">96</value>
			<value level="3">97</value>
		</magicLevel>
		<mpConsume>20</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<nextAction>ATTACK</nextAction>
		<conditions>
			<condition name="OpCheckCastRange">
				<distance>400</distance>
			</condition>
		</conditions>
		<!-- <effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">132687</value>
					<value level="2">138779</value>
					<value level="3">168092</value>
				</power>
			</effect>
		</effects> -->
	</skill>
	<skill id="14055" toLevel="2" name="Power Shot">
		<icon>icon.skill0056</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">-150</value>
			<value level="2">-200</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>
			<value level="1">92</value>
			<value level="2">93</value>
		</magicLevel>
		<mpConsume>
			<value level="1">83</value>
			<value level="2">84</value>
		</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">12411</value>
					<value level="2">12977</value>
				</power>
			</effect>
		</effects>
	</skill>
</list>
