<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="6700" toLevel="1" name="Ball Trapping Orodriel Agathion Cute Trick">
		<!-- See Agathion's cute trick. -->
		<icon>icon.skill_agathion_cute</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>7000</hitTime>
		<isMagic>2</isMagic>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<conditions>
			<condition name="OpNeedAgathion" />
		</conditions>
	</skill>
	<skill id="6701" toLevel="1" name="Penalty Kick Agathion Cute Trick">
		<!-- See Agathion's cute trick. -->
		<icon>icon.skill_agathion_cute</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>7000</hitTime>
		<isMagic>2</isMagic>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<conditions>
			<condition name="OpNeedAgathion" />
		</conditions>
	</skill>
	<skill id="6705" toLevel="1" name="Stun">
		<!-- Immobilizes and stuns target. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>100</castRange>
		<effectPoint>-1033</effectPoint>
		<effectRange>300</effectRange>
		<fanRange>0;0;300;65</fanRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>83</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<trait>SHOCK</trait>
	</skill>
	<skill id="6706" toLevel="1" name="Piercing Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>PD_DOWN</abnormalType>
		<activateRate>80</activateRate> <!-- FIXME: Base Land Rate unconformed -->
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>SQUARE</affectScope>
		<castRange>100</castRange>
		<effectPoint>-1349</effectPoint>
		<effectRange>200</effectRange>
		<fanRange>0;180;200;300</fanRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>83</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<effects>
			<!-- FIXME: Time value is unconfirmed -->
			<effect name="PhysicalDefence">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6707" toLevel="1" name="Self-destruction">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<effectPoint>-1000</effectPoint>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<reuseDelay>30000</reuseDelay>
	</skill>
	<skill id="6708" toLevel="1" name="Self-destruction">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-1000</effectPoint>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<reuseDelay>30000</reuseDelay>
	</skill>
	<skill id="6709" toLevel="1" name="Multi Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>300</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>500</effectRange>
		<fanRange>0;0;300;65</fanRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>83</mpConsume>
		<reuseDelay>30000</reuseDelay>
	</skill>
	<skill id="6710" toLevel="1" name="Etis Haste">
		<!-- Atk. Spd. increases by 10%. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<effectPoint>1000</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<reuseDelay>20000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="6711" toLevel="3" name="Etis Power Up">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<effectPoint>1000</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<reuseDelay>20000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="6714" toLevel="1" name="Wind Walk of Elcadia">
		<!-- Increases Speed by 33 for 10 min. -->
		<icon>icon.skill1204</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="Speed">
				<amount>33</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6715" toLevel="1" name="Haste of Elcadia">
		<!-- Increases Atk. Spd. by 33% for 10 min. -->
		<icon>icon.skill1086</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttackSpeed">
				<amount>33</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6716" toLevel="1" name="Might of Elcadia">
		<!-- For 10 min., P. Atk. +15%. -->
		<icon>icon.skill1068</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6717" toLevel="1" name="Berserker Spirit of Elcadia">
		<!-- For 10 min., party member's P. Def. -8%, M. Def. -16%, Evasion -4, P. Atk. +8%, M. Atk. +16%, Atk. Spd. +8%, Casting Spd. +8%, Speed +8. -->
		<icon>icon.skill1062</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>BERSERKER</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>16</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>8</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>16</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>-4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>16</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6718" toLevel="1" name="Death Whisper of Elcadia">
		<!-- For 10 min., P. Critical Damage +35%. -->
		<icon>icon.skill1242</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>CRITICAL_DMG_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="CriticalDamage">
				<amount>35</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6719" toLevel="1" name="Guidance of Elcadia">
		<!-- For 10 min., P. Accuracy +4. -->
		<icon>icon.skill1240</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>HIT_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6720" toLevel="1" name="Focus of Elcadia">
		<!-- For 10 min., P. Critical Rate +30%. -->
		<icon>icon.skill1077</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6721" toLevel="1" name="Empower of Elcadia">
		<!-- Increases M. Atk. by 75% for 10 min. -->
		<icon>icon.skill1059</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>MA_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="MagicalAttack">
				<amount>75</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6722" toLevel="1" name="Acumen of Elcadia">
		<!-- Increases Casting Spd. by 30% for 10 min. -->
		<icon>icon.skill1085</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>CASTING_TIME_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>495</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="MagicalAttackSpeed">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6723" toLevel="1" name="Concentration of Elcadia">
		<!-- For 20 min., Casting Interruption Rate -53%. -->
		<icon>icon.skill1078</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>7</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>CANCEL_PROB_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>624</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="ReduceCancel">
				<amount>-53</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6724" toLevel="1" name="Greater Heal of Elcadia">
		<!-- Recovers the target's HP by 450 power and recovers 30 HP per second for 15 sec. -->
		<icon>icon.skill1217</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>6</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<activateRate>0</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>600</castRange>
		<effectPoint>520</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<reuseDelay>3000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="HealOverTime">
				<power>30</power>
				<ticks>1</ticks>
			</effect>
			<effect name="Heal">
				<power>450</power>
			</effect>
		</effects>
	</skill>
	<skill id="6725" toLevel="1" name="Blessed Blood of Elcadia">
		<!-- For 15 sec., bestows the blessing of blood upon party members. Recovers HP by a certain rate when being attacked. -->
		<icon>icon.skill1218</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>BLESS_THE_BLOOD</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>600</castRange>
		<effectPoint>1</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<reuseDelay>20000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<staticReuse>true</staticReuse>
		<effects>
			<effect name="TriggerSkillByDamageReceived">
				<!-- Bless the Blood -->
				<attackerType>Creature</attackerType>
				<chance>60</chance>
				<targetType>SELF</targetType>
				<minDamage>60</minDamage>
				<skillId>6726</skillId> <!-- Blessed Blood -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="6726" toLevel="1" name="Blessed Blood">
		<icon>icon.skill3080</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<reuseDelay>4000</reuseDelay>
		<staticReuse>true</staticReuse>
		<effects>
			<effect name="Hp">
				<amount>687</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6727" toLevel="1" name="Vampiric Rage of Elcadia">
		<!-- For 10 min., with a certain chance, absorbs 9% of the inflicted damage as HP. -->
		<icon>icon.skill1268</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>5</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>VAMPIRIC_ATTACK</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>1</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="VampiricAttack">
				<amount>9</amount>
				<chance>80</chance>
			</effect>
		</effects>
	</skill>
	<skill id="6728" toLevel="1" name="Recharge of Elcadia">
		<!-- Recovers MP up to 136 power depending on the target's level. -->
		<icon>icon.skill1013</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>400</castRange>
		<effectPoint>655</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>6000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="ManaHealByLevel">
				<power>136</power>
			</effect>
		</effects>
	</skill>
	<skill id="6729" toLevel="1" name="Holy Attack Resistance of Elcadia">
		<!-- For 10 min., Holy Resistance +30. -->
		<icon>icon.skill1392</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>600</abnormalTime>
		<abnormalType>ARMOR_HOLY</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>400</castRange>
		<effectPoint>655</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<reuseDelay>2000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="DefenceAttribute">
				<amount>30</amount>
				<attribute>DARK</attribute>
			</effect>
		</effects>
	</skill>
	<skill id="6730" toLevel="1" name="Greater Battle Heal of Elcadia">
		<!-- Quickly recovers the target's HP by 858 power. -->
		<icon>icon.skill1218</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>600</castRange>
		<effectPoint>468</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<reuseDelay>1000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>858</power>
			</effect>
		</effects>
	</skill>
	<skill id="6731" toLevel="1" name="Etis Shadow">
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>STEALTH</abnormalType>
		<abnormalVisualEffect>STEALTH</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>150</castRange>
		<effectPoint>1000</effectPoint>
		<effectRange>200</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<specialLevel>-1</specialLevel>
		<conditions>
			<condition name="OpTargetNpc">
				<npcIds>
					<item>18950</item>
					<item>18951</item>
				</npcIds>
			</condition>
		</conditions>
	</skill>
	<skill id="6732" toLevel="1" name="Mirage">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectRange>2000</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<effectPoint>-10000</effectPoint>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>43</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="TargetCancel" />
		</effects>
	</skill>
	<skill id="6733" toLevel="1" name="Antharas' Stigma">
		<!-- Antharas' stigma. After 10 sec., spreads the Stigma Aftermath nearby, which decreases 400 HP per second and 150 MP. -->
		<icon>icon.skill5860</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>TIME_BOMB</abnormalType>
		<abnormalVisualEffect>TIME_BOMB</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>2000</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>2000</effectRange>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>2</isMagic>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="6734" toLevel="1" name="Energy of Destruction">
		<!-- For 5 sec., inflicted with poison with 800 HP decrease per sec. -->
		<icon>icon.skill0129</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>10</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<effectPoint>-100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<trait>POISON</trait>
	</skill>
	<skill id="6735" toLevel="1" name="Petrifies the target.">
		<!-- Petrified and unable to move temporarily. -->
		<icon>icon.skill4111</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>TURN_STONE</abnormalType>
		<abnormalVisualEffect>FLESH_STONE</abnormalVisualEffect>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>100</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>-1000</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6736" toLevel="1" name="Fierce Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>SQUARE</affectScope>
		<castRange>2000</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>2000</effectRange>
		<fanRange>0;0;500;40</fanRange>
		<hitTime>4000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<reuseDelay>1000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6737" toLevel="1" name="Heal">
		<icon>icon.skill1401</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>FRIEND</affectObject>
		<affectRange>2000</affectRange>
		<affectScope>RANGE</affectScope>
		<effectPoint>546</effectPoint>
		<hitTime>90000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>130</mpConsume>
		<reuseDelay>1000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>800</power>
			</effect>
		</effects>
	</skill>
	<skill id="6738" toLevel="1" name="Hold">
		<!-- Holds the target. -->
		<icon>icon.skill4047</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>ROOT_MAGICALLY</abnormalType>
		<abnormalVisualEffect>ROOT</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<castRange>2000</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>2000</effectRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>80</mpConsume>
		<reuseDelay>1000</reuseDelay>
		<trait>HOLD</trait>
	</skill>
	<skill id="6739" toLevel="1" name="Presentation - Behemoth Leader Casting Preparation">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>100</effectPoint>
		<hitTime>45000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6740" toLevel="1" name="Presentation - Behemoth Leader Casting">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6741" toLevel="1" name="Presentation - Behemoth Leader Casting Failure">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>100</effectPoint>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6742" toLevel="1" name="Presentation - Behemoth Object Channeling">
		<icon>icon.skill0000</icon>
		<operateType>CA1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>2000</castRange>
		<channelingStart>1</channelingStart>
		<channelingTickInterval>0</channelingTickInterval>
		<effectPoint>100</effectPoint>
		<effectRange>2000</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>90000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6743" toLevel="1" name="Dark Wound">
		<!-- Dark attribute is weakened; inflicted with bleed and 200 HP is decreased every second. -->
		<icon>icon.skill0096</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>30</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<blockedInOlympiad>false</blockedInOlympiad>
		<castRange>1500</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>2000</effectRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>25</mpConsume>
		<reuseDelay>1000</reuseDelay>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<trait>BLEED</trait>
		<effects>
			<effect name="DamOverTime">
				<power>200</power>
				<ticks>1</ticks>
			</effect>
			<effect name="DefenceAttribute">
				<amount>-60</amount>
				<attribute>DARK</attribute>
			</effect>
		</effects>
	</skill>
	<skill id="6744" toLevel="1" name="Dark Storm">
		<!-- Casting Spd. decreases by 90% and Atk. Spd. decreases by 30%. -->
		<icon>icon.skill1386</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>CASTING_TIME_UP</abnormalType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>25</mpConsume>
		<reuseDelay>1000</reuseDelay>
	</skill>
	<skill id="6745" toLevel="1" name="Dark Space">
		<icon>icon.skill1445</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>ARMOR_UNHOLY</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<mpConsume>25</mpConsume>
		<reuseDelay>1000</reuseDelay>
		<specialLevel>-1</specialLevel>
	</skill>
	<skill id="6746" toLevel="1" name="Dark Space">
		<!-- Dark attribute is weakened. -->
		<icon>icon.skill1445</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>ARMOR_UNHOLY</abnormalType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>25</mpConsume>
		<reuseDelay>1000</reuseDelay>
	</skill>
	<skill id="6747" toLevel="1" name="Dark Blade">
		<icon>icon.skill1445</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>60</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>150</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>80</mpConsume>
		<reuseDelay>1000</reuseDelay>
	</skill>
	<skill id="6748" toLevel="1" name="Stun Attack">
		<!-- Inflicts Stun for 5 sec. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>150</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>33</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="6749" toLevel="1" name="Devil Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6750" toLevel="1" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>33</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6751" toLevel="1" name="Blow Attack">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>33</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="6752" toLevel="1" name="Revival">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<abnormalTime>60</abnormalTime>
		<affectScope>SINGLE</affectScope>
		<effectPoint>562</effectPoint>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>158</mpConsume>
		<reuseDelay>300000</reuseDelay>
		<conditions>
			<condition name="RemainHpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
				<affectType>CASTER</affectType>
			</condition>
		</conditions>
		<effects>
			<effect name="HealPercent">
				<power>100</power>
			</effect>
		</effects>
	</skill>
	<skill id="6753" toLevel="1" name="Death Blow">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-337</effectPoint>
		<effectRange>300</effectRange>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>77</mpConsume>
		<trait>DEATH</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6754" toLevel="1" name="Bleed">
		<!-- Inflicted with bleed and -200 HP every sec. -->
		<icon>icon.skill0096</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<effectPoint>-10000</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>100</mpConsume>
		<reuseDelay>10000</reuseDelay>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<trait>BLEED</trait>
		<effects>
			<effect name="DamOverTime">
				<power>200</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="6755" toLevel="1" name="Devil Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>78</mpConsume>
		<effects>
			<effect name="MagicalDamage">
				<power>100</power>
			</effect>
		</effects>
	</skill>
	<skill id="6756" toLevel="1" name="Death Talon">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<effectPoint>-10000</effectPoint>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>155</mpConsume>
		<effects>
			<effect name="MagicalDamage">
				<power>100</power>
			</effect>
		</effects>
	</skill>
	<skill id="6757" toLevel="1" name="Slow">
		<!-- Decreases the enemy's Speed by 30% for 15 sec. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>SPEED_DOWN</abnormalType>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-655</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>69</mpConsume>
		<reuseDelay>60000</reuseDelay>
		<effects>
			<effect name="Speed">
				<amount>-30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6758" toLevel="1" name="Rage">
		<icon>icon.skill1068</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>RANGE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100000</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>69</mpConsume>
		<reuseDelay>60000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>35</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6759" toLevel="1" name="Death Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-897</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="6760" toLevel="1" name="Dragon Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6761" toLevel="1" name="Dragon Blow Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<effectPoint>-1000</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>158</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>10000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6762" toLevel="1" name="Rage">
		<icon>icon.skill1068</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ATTACK_TIME_DOWN</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100000</effectPoint>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>69</mpConsume>
		<reuseDelay>300000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>35</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6763" toLevel="1" name="Dragon Earth Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6764" toLevel="1" name="Dragon Earth Shot">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-897</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6765" toLevel="1" name="Full Recovery">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>562</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<effects>
			<effect name="HealPercent">
				<power>100</power>
			</effect>
		</effects>
	</skill>
	<skill id="6766" toLevel="1" name="Earth Tremor">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>78</mpConsume>
		<effects>
			<effect name="MagicalDamage">
				<power>150</power>
			</effect>
		</effects>
	</skill>
	<skill id="6767" toLevel="1" name="Earthquake">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<effectPoint>-10000</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>155</mpConsume>
	</skill>
	<skill id="6768" toLevel="1" name="Stun Attack">
		<!-- Inflicts Stun for 5 sec. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>5</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>150</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>158</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="6769" toLevel="1" name="Petrify">
		<!-- Petrified and unable to move temporarily. -->
		<icon>icon.skill4111</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>TURN_STONE</abnormalType>
		<abnormalVisualEffect>FLESH_STONE</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>EARTH</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>155</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="6770" toLevel="1" name="Heal">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectRange>900</affectRange>
		<affectScope>SINGLE</affectScope>
		<castRange>900</castRange>
		<effectPoint>100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>130</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>800</power>
			</effect>
		</effects>
	</skill>
	<skill id="6771" toLevel="1" name="Devil Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>79</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6772" toLevel="1" name="Power Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>33</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6773" toLevel="1" name="Rage">
		<icon>icon.skill1068</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>RANGE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100000</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>69</mpConsume>
		<reuseDelay>60000</reuseDelay>
		<specialLevel>-1</specialLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>35</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6774" toLevel="1" name="Stun Attack">
		<!-- Attacks and stun the target for 10 sec. -->
		<icon>icon.skill0100</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>150</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>85</mpConsume>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="6775" toLevel="1" name="Soul Breath">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectScope>FAN</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-1000</effectPoint>
		<effectRange>200</effectRange>
		<fanRange>0;0;150;60</fanRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>85</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6776" toLevel="1" name="Paralysis">
		<!-- Causes the enemy's body to go completely rigid for 10 sec. and causes paralysis for 10 sec. -->
		<icon>icon.skill1170</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>MAGIC</basicProperty>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>2500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>1</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>69</mpConsume>
		<trait>PARALYZE</trait>
		<effects>
			<effect name="Speed">
				<amount>-50</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>-50</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>-50</amount>
				<mode>PER</mode>
			</effect>
		</effects>
		<endEffects>
			<effect name="CallSkill">
				<skillId>6779</skillId> <!-- Paralysis -->
				<skillLevel>1</skillLevel>
			</effect>
		</endEffects>
	</skill>
	<skill id="6777" toLevel="1" name="Bleeding Gash">
		<!-- For 20 sec., slows down Speed and inflicts bleed so that -200 HP every sec. Inflicted with paralysis for 10 sec. -->
		<icon>icon.skill0096</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>20</abnormalTime>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<effectPoint>-323</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>2</lvlBonusRate>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>89</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<trait>BLEED</trait>
		<effects>
			<effect name="DamOverTime">
				<power>200</power>
				<ticks>1</ticks>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="6778" toLevel="1" name="Devil Strike">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>120</attributeValue>
		<castRange>150</castRange>
		<effectPoint>-10000</effectPoint>
		<effectRange>300</effectRange>
		<hitTime>1500</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<mpConsume>89</mpConsume>
		<effects>
			<effect name="PhysicalDamage">
				<power>15000</power>
				<criticalChance>20</criticalChance>
			</effect>
		</effects>
	</skill>
	<skill id="6779" toLevel="1" name="Paralysis">
		<!-- Body is rigid and paralyzed. -->
		<icon>icon.skill1170</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime> <!-- FIXME: value unconfirmed -->
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>1</effectPoint>
		<isDebuff>true</isDebuff>
		<isMagic>2</isMagic> <!-- Static Skill -->
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
		<trait>PARALYZE</trait>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="6780" toLevel="1" name="Presentation - Portal Ray Combined Light Maintenance">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6781" toLevel="1" name="Presentation - Red Portal Ray">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6782" toLevel="1" name="Presentation - Blue Portal Ray">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6783" toLevel="1" name="Presentation - Portal 2_boom_up">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6784" toLevel="1" name="Presentation - Eris' Suffering">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>4000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6785" toLevel="1" name="Presentation - Dialogue with Etis 1">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>4000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6786" toLevel="1" name="Presentation - Etis Transformation Effect">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6787" toLevel="1" name="Presentation - Etis Pre-Transformation">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>4000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6788" toLevel="1" name="Presentation - Etis Post-Transformation">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6789" toLevel="1" name="Presentation - Etis' Defeat">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>6000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6790" toLevel="1" name="Presentation - Etis Kneeling">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>30000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6791" toLevel="1" name="Presentation - Dialogue with Elcadia 1">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6792" toLevel="1" name="Presentation - Dialogue with Elcadia 2">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>30000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6793" toLevel="1" name="Presentation - Dialogue with Elcadia 3">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>2000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6794" toLevel="1" name="Presentation - Elcadia Casting 1">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>3000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6795" toLevel="1" name="Presentation - Elcadia Casting 2">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<hitTime>5000</hitTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6796" toLevel="1" name="Presentation - Meteor Effect">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6797" toLevel="1" name="Presentation - Eris Scatter Effect">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6798" toLevel="1" name="Presentation - Etis Summon Effect">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
	<skill id="6799" toLevel="1" name="Presentation - Portal 2_boom_down">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<blockedInOlympiad>false</blockedInOlympiad>
		<hitCancelTime>0</hitCancelTime>
		<magicCriticalRate>-5</magicCriticalRate>
		<magicLevel>85</magicLevel>
	</skill>
</list>
