<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="18024" toLevel="26" name="<PERSON><PERSON><PERSON>'s Talisman">
		<!-- Level 1: When equipped, CON +1. -->
		<!-- Level 2: When equipped, CON, MEN +1. -->
		<!-- Level 3: When equipped, CON, MEN, DEX +1. -->
		<!-- Level 4: When equipped, CON, MEN, DEX, WIT +1. -->
		<!-- Level 5: When equipped, CON, MEN, DEX, WIT, STR +1. -->
		<!-- Level 6: When equipped, CON, MEN, DEX, WIT, STR, INT +1. -->
		<!-- Level 7: When equipped, CON +2, MEN, DEX, WIT, STR, INT +1. -->
		<!-- Level 8: When equipped, CON, MEN +2, DEX, WIT, STR, INT +1. -->
		<!-- Level 9: When equipped, CON, MEN, DEX +2, WIT, STR, INT +1. -->
		<!-- Level 10: When equipped, CON, MEN, DEX, WIT +2, STR, INT +1. -->
		<!-- Level 11: When equipped, CON, MEN, DEX, WIT, STR +2, INT +1. -->
		<!-- Level 12: When equipped, CON, MEN, DEX, WIT, STR, INT +2. Additionally has a chance of recovering 4% of damage inflicted on the enemy as HP or 2% as MP. -->
		<!-- Level 13: When equipped, CON +3, MEN, DEX, WIT, STR, INT +2. Additionally has a chance of recovering 4% of damage inflicted on the enemy as HP or 2% as MP. -->
		<!-- Level 14: When equipped, CON, MEN +3, DEX, WIT, STR, INT +2. Additionally has a chance of recovering 4% of damage inflicted on the enemy as HP or 2% as MP. -->
		<!-- Level 15: When equipped, CON, MEN, DEX +3, WIT, STR, INT +2. Additionally has a chance of recovering 4% of damage inflicted on the enemy as HP or 2% as MP. -->
		<!-- Level 16: When equipped, CON, MEN, DEX, WIT +3, STR, INT +2. Additionally has a chance of recovering 4% of damage inflicted on the enemy as HP or 2% as MP. -->
		<!-- Level 17: When equipped, CON, MEN, DEX, WIT, STR +3, INT +2. Additionally has a chance of recovering 4% of damage inflicted on the enemy as HP or 2% as MP. -->
		<!-- Level 18: CON, MEN, DEX, WIT, STR, INT +3, P./ M. Skill Cooldown -7%. With a certain chance, absorbs 8% of the inflicted damage as HP and 4% as MP. Effects of two identical talismans do not stack. Cannot be dropped. -->
		<!-- Level 19: When equipped, CON +4, MEN, DEX, WIT, STR, INT +3, P./ M. Skill Cooldown -7%. Additionally, has a chance of recovering 8% of damage inflicted on the enemy as HP or 4% as MP. -->
		<!-- Level 20: When equipped, CON, MEN +4, DEX, WIT, STR, INT +3, P./ M. Skill Cooldown -7%. Additionally, has a chance of recovering 8% of damage inflicted on the enemy as HP or 4% as MP. -->
		<!-- Level 21: When equipped, CON, MEN, DEX +4, WIT, STR, INT +3, P./ M. Skill Cooldown -7%. Additionally, has a chance of recovering 8% of damage inflicted on the enemy as HP or 4% as MP. -->
		<!-- Level 22: When equipped, CON, MEN, DEX, WIT +4, STR, INT +3, P./ M. Skill Cooldown -7%. Additionally, has a chance of recovering 8% of damage inflicted on the enemy as HP or 4% as MP. -->
		<!-- Level 23: When equipped, CON, MEN, DEX, WIT, STR +4, INT +3, P./ M. Skill Cooldown -7%. Additionally, has a chance of recovering 8% of damage inflicted on the enemy as HP or 4% as MP. -->
		<!-- Level 24: CON, MEN, DEX, WIT, STR, INT +4, P./ M. Skill Cooldown -7%. With a certain chance, absorbs 16% of the inflicted damage as HP and 8% as MP. Effects of two identical talismans do not stack. Cannot be dropped. -->
		<!-- Level 25: CON, MEN, DEX, WIT, STR, INT +6, P./ M. Skill Cooldown -7%. With a certain chance, absorbs 16% of the inflicted damage as HP and 8% as MP. Effects of two identical talismans do not stack. Cannot be dropped. -->
		<!-- Level 26: CON, MEN, DEX, WIT, STR, INT +8, P./ M. Skill Cooldown -7%. With a certain chance, absorbs 16% of the inflicted damage as HP and 8% as MP. Effects of two identical talismans do not stack. Cannot be dropped. -->
		<icon>
			<value level="1">icon.etc_all_stat_talisman_i00</value>
			<value level="2">icon.etc_all_stat_talisman_i00</value>
			<value level="3">icon.etc_all_stat_talisman_i00</value>
			<value level="4">icon.etc_all_stat_talisman_i00</value>
			<value level="5">icon.etc_all_stat_talisman_i00</value>
			<value level="6">icon.etc_all_stat_talisman_i00</value>
			<value level="7">icon.etc_all_stat_talisman_i01</value>
			<value level="8">icon.etc_all_stat_talisman_i01</value>
			<value level="9">icon.etc_all_stat_talisman_i01</value>
			<value level="10">icon.etc_all_stat_talisman_i01</value>
			<value level="11">icon.etc_all_stat_talisman_i01</value>
			<value level="12">icon.etc_all_stat_talisman_i01</value>
			<value level="13">icon.etc_all_stat_talisman_i02</value>
			<value level="14">icon.etc_all_stat_talisman_i02</value>
			<value level="15">icon.etc_all_stat_talisman_i02</value>
			<value level="16">icon.etc_all_stat_talisman_i02</value>
			<value level="17">icon.etc_all_stat_talisman_i02</value>
			<value level="18">icon.etc_all_stat_talisman_i02</value>
			<value level="19">icon.etc_all_stat_talisman_i02</value>
			<value level="20">icon.etc_all_stat_talisman_i02</value>
			<value level="21">icon.etc_all_stat_talisman_i02</value>
			<value level="22">icon.etc_all_stat_talisman_i02</value>
			<value level="23">icon.etc_all_stat_talisman_i02</value>
			<value level="24">icon.etc_all_stat_talisman_i02</value>
		</icon>
		<operateType>P</operateType>
		<effects>
			<effect name="StatUp">
				<amount>
					<value fromLevel="1" toLevel="6">1</value>
					<value fromLevel="7" toLevel="12">2</value>
					<value fromLevel="13" toLevel="18">3</value>
					<value fromLevel="19" toLevel="21">4</value>
					<value fromLevel="22" toLevel="24">5</value>
					<value level="25">6</value>
					<value fromLevel="26" toLevel="30">7</value>
				</amount>
				<stat>CON</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="2" toLevel="7">1</value>
					<value fromLevel="8" toLevel="13">2</value>
					<value fromLevel="14" toLevel="18">3</value>
					<value fromLevel="19" toLevel="21">4</value>
					<value fromLevel="22" toLevel="24">5</value>
					<value fromLevel="25" toLevel="26">6</value>
					<value fromLevel="27" toLevel="30">7</value>
				</amount>
				<stat>MEN</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="3" toLevel="8">1</value>
					<value fromLevel="9" toLevel="14">2</value>
					<value fromLevel="15" toLevel="19">3</value>
					<value fromLevel="20" toLevel="22">4</value>
					<value fromLevel="23" toLevel="24">5</value>
					<value fromLevel="25" toLevel="27">6</value>
					<value fromLevel="28" toLevel="30">7</value>
				</amount>
				<stat>DEX</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="4" toLevel="9">1</value>
					<value fromLevel="10" toLevel="15">2</value>
					<value fromLevel="16" toLevel="19">3</value>
					<value fromLevel="20" toLevel="22">4</value>
					<value fromLevel="23" toLevel="24">5</value>
					<value fromLevel="25" toLevel="28">6</value>
					<value fromLevel="29" toLevel="30">7</value>
				</amount>
				<stat>WIT</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="5" toLevel="10">1</value>
					<value fromLevel="11" toLevel="16">2</value>
					<value fromLevel="17" toLevel="20">3</value>
					<value fromLevel="21" toLevel="23">4</value>
					<value level="24">5</value>
					<value fromLevel="25" toLevel="29">6</value>
					<value level="30">7</value>
				</amount>
				<stat>STR</stat>
			</effect>
			<effect name="StatUp">
				<amount>
					<value fromLevel="6" toLevel="11">1</value>
					<value fromLevel="12" toLevel="17">2</value>
					<value fromLevel="18" toLevel="20">3</value>
					<value fromLevel="21" toLevel="23">4</value>
					<value level="24">5</value>
					<value fromLevel="25" toLevel="29">6</value>
					<value level="30">7</value>
				</amount>
				<stat>INT</stat>
			</effect>
			<effect name="Reuse">
				<amount>
					<value level="18">-7</value>
					<value level="19">-7</value>
					<value level="20">-7</value>
					<value level="21">-7</value>
					<value level="22">-7</value>
					<value level="23">-7</value>
					<value level="24">-7</value>
					<value level="25">-7</value>
					<value level="26">-7</value>
				</amount>
				<mode>PER</mode>
				<magicType>0</magicType>
			</effect>
			<effect name="Reuse">
				<amount>
					<value level="18">-7</value>
					<value level="19">-7</value>
					<value level="20">-7</value>
					<value level="21">-7</value>
					<value level="22">-7</value>
					<value level="23">-7</value>
					<value level="24">-7</value>
					<value level="25">-7</value>
					<value level="26">-7</value>
				</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
			<effect name="MaxHp">
				<amount>
					<value fromLevel="1" toLevel="15">0</value>
					<value level="16">100</value>
					<value level="17">200</value>
					<value level="18">300</value>
					<value level="19">400</value>
					<value level="20">500</value>
					<value level="21">600</value>
					<value level="22">700</value>
					<value level="23">800</value>
					<value level="24">900</value>
					<value level="25">1000</value>
					<value level="26">1200</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>
					<value fromLevel="1" toLevel="25">0</value>
					<value fromLevel="12" toLevel="17">4</value>
					<value fromLevel="18" toLevel="24">8</value>
					<value fromLevel="25" toLevel="26">16</value>
				</amount>
				<chance>30</chance>
			</effect>
			<effect name="MpVampiricAttack">
				<amount>
					<value fromLevel="1" toLevel="25">0</value>
					<value fromLevel="12" toLevel="17">2</value>
					<value fromLevel="18" toLevel="24">4</value>
					<value fromLevel="25" toLevel="26">8</value>
				</amount>
				<chance>30</chance>
			</effect>
		</effects>
	</skill>
</list>
