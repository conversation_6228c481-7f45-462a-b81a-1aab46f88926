<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="15500" toLevel="1" name="Major Energy of Life">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15501" toLevel="1" name="Superior Energy of Life">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15502" toLevel="1" name="Energy of Mana">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15503" toLevel="1" name="Major Energy of Mana">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15504" toLevel="1" name="Superior Energy of Mana">
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15506" toLevel="1" name="Wondrous Herb of Power">
		<!-- For 1 h. 30 sec., P. Atk. +100%. -->
		<icon>icon.etc_warrior_set_i00</icon>
		<operateType>A2</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15507" toLevel="1" name="Wondrous Herb of Magic">
		<!-- For 1 h. 30 sec., M. Atk. +400%. -->
		<icon>icon.etc_magic_set_i00</icon>
		<operateType>A2</operateType>
		<castRange>2000</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>100</hitTime>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15508" toLevel="4" name="Special Armor">
		<!-- Special armor reduces damage received from servitors. -->
		<icon>icon.skill0000</icon>
		<operateType>P</operateType>
	</skill>
	<skill id="15509" toLevel="1" name="Laido Destruction">
		<!-- Powerful attack on enemies near target. -->
		<icon>icon.skill0036</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15510" toLevel="1" name="Laido Repair">
		<!-- Repairs ally. -->
		<icon>icon.skill1217</icon>
		<operateType>A1</operateType>
		<castRange>50</castRange>
		<effectPoint>100</effectPoint>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
		<reuseDelay>7000</reuseDelay>
	</skill>
	<skill id="15511" toLevel="1" name="Laido Punch">
		<!-- Powerful attack on enemy. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15512" toLevel="1" name="Single Stroke">
		<!-- Attacks a single enemy. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15513" toLevel="1" name="Vital Hit">
		<!-- Attacks a single enemy. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15514" toLevel="1" name="Bloody Chain">
		<!-- Connects target and nearby enemies with chains for damage. -->
		<icon>icon.skill0036</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>2000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15515" toLevel="1" name="Penetrating Eyes">
		<!-- Emits power from the eyes for powerful damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>100</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
		<reuseDelay>4000</reuseDelay>
	</skill>
	<skill id="15516" toLevel="1" name="Summon Giant Illusion">
		<!-- Twists the flow of time to summon a bigger version of the self upon death (Presentation). -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15517" toLevel="1" name="Chaos Attack">
		<!-- Inflicts powerful physical damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>70</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15518" toLevel="2" name="Confuse Body">
		<!-- Level 1: Confuses the interior of the target's body, decreasing P. Def./ M. Def. by 10%. -->
		<!-- Level 2: Confuses the interior of the target's body, decreasing P. Def./ M. Def. by 15%. -->
		<icon>icon.skill1422</icon>
		<operateType>A2</operateType>
		<castRange>300</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
	</skill>
	<skill id="15519" toLevel="1" name="Summon Undead">
		<!-- Summons real body. (Presentation) -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15520" toLevel="1" name="Rush Raid">
		<!-- Powerful rush attack on enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15521" toLevel="1" name="Flailing Spin">
		<!-- Inflicts powerful physical damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>70</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15522" toLevel="1" name="Dark Poison">
		<!-- Damages enemy with dark poison and drains HP gradually. -->
		<icon>icon.skill4035</icon>
		<operateType>A2</operateType>
		<castRange>40</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
		<reuseDelay>4000</reuseDelay>
	</skill>
	<skill id="15523" toLevel="1" name="Accelerate">
		<!-- Increases Speed. -->
		<icon>icon.skill1204</icon>
		<operateType>A2</operateType>
		<effectPoint>2</effectPoint>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15524" toLevel="1" name="Suck Blood">
		<!-- Sucks blood from the enemy, continuously draining HP. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15525" toLevel="2" name="Rush Attack">
		<!-- Powerful rush attack on enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>100</hitTime>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15526" toLevel="1" name="Karamar Breaker">
		<!-- Powerful attack on enemies near target. -->
		<icon>icon.skill0036</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15527" toLevel="1" name="Chaos Breath">
		<!-- AoE M. Atk. On target's area. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>300</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15528" toLevel="1" name="Kaonel Rush">
		<!-- Powerful rush attack on enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>100</hitTime>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15529" toLevel="1" name="Absorb Spirit">
		<!-- Soul is drained. P. Def./ M. Def. -30%, and Atk. Spd. +10%. -->
		<icon>icon.skill1422</icon>
		<operateType>A2</operateType>
		<castRange>300</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
	</skill>
	<skill id="15530" toLevel="1" name="Slumber">
		<!-- Immobilizes and puts a target into sleep. -->
		<icon>icon.skill4046</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>13</mpConsume>
		<reuseDelay>8000</reuseDelay>
	</skill>
	<skill id="15531" toLevel="1" name="Hold">
		<!-- Holds the target. -->
		<icon>icon.skill4047</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>13</mpConsume>
		<reuseDelay>8000</reuseDelay>
	</skill>
	<skill id="15532" toLevel="1" name="Pupil Focus">
		<!-- Emits power from the eyes for powerful damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>40</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15533" toLevel="1" name="Spiteful Grudge">
		<!-- Damages enemy with poison and drains HP gradually. -->
		<icon>icon.skill4035</icon>
		<operateType>A2</operateType>
		<castRange>40</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15534" toLevel="1" name="Mutilate Body">
		<!-- Cuts enemy for powerful damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>70</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15535" toLevel="1" name="Bloody Breath">
		<!-- Breathes a crimson breath on enemies within 120 degrees in front of you to inflict damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>300</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15536" toLevel="1" name="Chaotic Mana">
		<!-- You are in the Land of Chaos. You can feel Krogel's power around you. -->
		<icon>icon.skill0046</icon>
		<operateType>A2</operateType>
		<effectPoint>1</effectPoint>
		<hitTime>500</hitTime>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15537" toLevel="2" name="Blood Sacrifice: Decrease Speed">
		<!-- Level 1: The target's HP +50%, also inflicts debuff. -->
		<!-- Level 2: The target's HP +100%, also inflicts debuff. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<isDebuff>true</isDebuff>
	</skill>
	<skill id="15538" toLevel="2" name="Blood Sacrifice: Decrease P. Accuracy">
		<!-- Level 1: The target's HP +50%, also inflicts debuff. -->
		<!-- Level 2: The target's HP +100%, also inflicts debuff. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<isDebuff>true</isDebuff>
	</skill>
	<skill id="15539" toLevel="2" name="Blood Sacrifice: Decrease P. Def.">
		<!-- Level 1: The target's HP +50%, also inflicts debuff. -->
		<!-- Level 2: The target's HP +100%, also inflicts debuff. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<isDebuff>true</isDebuff>
	</skill>
	<skill id="15540" toLevel="2" name="Blood Sacrifice: Decrease M. Def.">
		<!-- Level 1: The target's HP +50%, also inflicts debuff. -->
		<!-- Level 2: The target's HP +100%, also inflicts debuff. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<isDebuff>true</isDebuff>
	</skill>
	<skill id="15541" toLevel="1" name="Flame Pillar">
		<!-- A pillar of fire rushes from beneath your target's feet for damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>300</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15542" toLevel="1" name="Rush Raid">
		<!-- Rush toward target, inflicts damage. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>100</hitTime>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15543" toLevel="1" name="Poison Fog">
		<!-- Throws poison dust on enemy, draining their HP. -->
		<icon>icon.skill4035</icon>
		<operateType>A2</operateType>
		<castRange>300</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-687</effectPoint>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15544" toLevel="1" name="Rush Attack">
		<!-- Powerful rush attack on enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A2</operateType>
		<castRange>300</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>100</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15545" toLevel="1" name="Cubit Attack">
		<!-- Powerful attack on enemies in a fan-shaped radius of you. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>200</castRange>
		<effectPoint>-680</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>2000</reuseDelay>
	</skill>
	<skill id="15546" toLevel="1" name="Dark Invisibility">
		<!-- Temporarily grants invisibility. -->
		<icon>icon.skill10784</icon>
		<operateType>A2</operateType>
		<effectPoint>2</effectPoint>
		<hitTime>1000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15547" toLevel="1" name="Point Attack">
		<!-- Swings sword at an enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15548" toLevel="1" name="Partial Healing">
		<!-- Restores a part of allies' HP. -->
		<icon>icon.skill1219</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<coolTime>500</coolTime>
		<effectPoint>694</effectPoint>
		<hitTime>1500</hitTime>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15549" toLevel="1" name="Soul Break">
		<!-- For 20 min., nearby enemies' Casting Spd. -20% and P. Critical Damage -30%. -->
		<icon>icon.skill0036</icon>
		<operateType>A2</operateType>
		<castRange>600</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15550" toLevel="1" name="Heart Destroyer">
		<!-- Powerful attack on enemy's vital point. Speed increases upon hit. Has a low chance of doing Half-kill. -->
		<icon>icon.skill10509</icon>
		<operateType>A2</operateType>
		<castRange>40</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-687</effectPoint>
		<hitTime>1000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15551" toLevel="1" name="Heavy Attack">
		<!-- Hits enemy's vital point, weakening P. Atk. -->
		<icon>icon.skill1160</icon>
		<operateType>A2</operateType>
		<castRange>200</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15552" toLevel="1" name="Hell's Prison">
		<!-- Target's P. Atk. -30%. -->
		<icon>icon.skill1068</icon>
		<operateType>A2</operateType>
		<castRange>200</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15554" toLevel="1" name="Broad Hit">
		<!-- Powerful attack on target and nearby enemies. -->
		<icon>icon.skill10016</icon>
		<operateType>A1</operateType>
		<castRange>400</castRange>
		<coolTime>1000</coolTime>
		<effectPoint>-2093</effectPoint>
		<hitTime>6800</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15555" toLevel="1" name="Cunning Strike">
		<!-- Hits enemy's vital point. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>200</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>2000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15556" toLevel="1" name="Provoke">
		<!-- Provokes target into attacking you by forcefully changing their target. -->
		<icon>icon.skill10027</icon>
		<operateType>A1</operateType>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15557" toLevel="1" name="Crane Strike">
		<!-- Decreases target's Atk. Spd. by 10%. -->
		<icon>icon.skill0101</icon>
		<operateType>A2</operateType>
		<castRange>300</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-867</effectPoint>
		<hitTime>1000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15558" toLevel="1" name="Soul Howl">
		<!-- Attacks enemy and decreases their Casting Spd. -->
		<icon>icon.skill4038</icon>
		<operateType>A2</operateType>
		<castRange>300</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-507</effectPoint>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
		<mpConsume>10</mpConsume>
	</skill>
	<skill id="15559" toLevel="1" name="Poison Attack">
		<!-- Powerful attack on the whole party and poison damage. -->
		<icon>icon.skill4035</icon>
		<operateType>A2</operateType>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>1000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>7000</reuseDelay>
	</skill>
	<skill id="15561" toLevel="1" name="Connected Drain">
		<!-- Bleeding for 5 sec. -->
		<icon>icon.skill0096</icon>
		<operateType>A2</operateType>
		<castRange>400</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-574</effectPoint>
		<hitTime>1000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15562" toLevel="1" name="Lethal Edge">
		<!-- Instantly kills a party member. -->
		<icon>icon.skill0223</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15564" toLevel="2" name="Theor's Attack">
		<!-- P. Atk. On a single enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>70</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>2000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15565" toLevel="1" name="Theor's AoE Attack">
		<!-- AoE P. Atk. On nearby enemies. -->
		<icon>icon.skill0036</icon>
		<operateType>A1</operateType>
		<effectPoint>-100</effectPoint>
		<hitTime>3000</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15566" toLevel="1" name="Disarm">
		<!-- Disarms nearby enemies. -->
		<icon>icon.skill0485</icon>
		<operateType>A2</operateType>
		<effectPoint>-100</effectPoint>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<trait>DISARM</trait>
	</skill>
	<skill id="15567" toLevel="1" name="Aerial Yoke">
		<!-- Aerial Yoke. -->
		<icon>icon.skill1042</icon>
		<operateType>A2</operateType>
		<effectPoint>-100</effectPoint>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<mpConsume>10</mpConsume>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15568" toLevel="1" name="Charge">
		<!-- Powerful rush attack on enemy. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<effectPoint>-100</effectPoint>
		<hitTime>100</hitTime>
		<mpConsume>10</mpConsume>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15570" toLevel="1" name="Master Siege Shield">
		<!-- For 30 sec., reflects 50% of received damage. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<coolTime>500</coolTime>
		<effectPoint>379</effectPoint>
		<hitTime>3000</hitTime>
		<mpConsume>100</mpConsume>
		<reuseDelay>150000</reuseDelay>
	</skill>
	<skill id="15571" toLevel="1" name="Master Siege Attack">
		<!-- Powerful attack on enemy. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<castRange>80</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-867</effectPoint>
		<hitTime>3000</hitTime>
		<mpConsume>100</mpConsume>
		<reuseDelay>1000</reuseDelay>
	</skill>
	<skill id="15572" toLevel="1" name="Krogel's Haste">
		<!-- Atk. Spd. +15%. -->
		<icon>icon.skill1086</icon>
		<operateType>A2</operateType>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
	</skill>
	<skill id="15574" toLevel="1" name="Master Siege Hammer">
		<!-- Powerful attack on castle doors and walls. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<coolTime>500</coolTime>
		<effectPoint>-709</effectPoint>
		<hitTime>5000</hitTime>
		<mpConsume>100</mpConsume>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15575" toLevel="1" name="Dedicated Blow">
		<!-- Deals a lot of physical damage to the target. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15576" toLevel="1" name="Dedicated Shot">
		<!-- Deals a lot of physical damage to the target. -->
		<icon>icon.skill0056</icon>
		<operateType>A1</operateType>
		<castRange>250</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15577" toLevel="1" name="Furious Blow">
		<!-- Powerful P. Atk. on enemy. -->
		<icon>icon.skill0110</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15578" toLevel="1" name="Furious Bombing">
		<!-- Powerful P. Atk. On enemies 180 degrees to the front of you. -->
		<icon>icon.skill1068</icon>
		<operateType>A1</operateType>
		<castRange>200</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>3000</hitTime>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15579" toLevel="1" name="Cursed Blow">
		<!-- Powerful P. Atk. on enemy. -->
		<icon>icon.skill0110</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15580" toLevel="1" name="Cursed Bombing">
		<!-- Powerful M. Atk. On target and nearby enemies. -->
		<icon>icon.skill1092</icon>
		<operateType>A1</operateType>
		<castRange>250</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15581" toLevel="1" name="Fearsome Blow">
		<!-- Powerful P. Atk. on enemy. -->
		<icon>icon.skill0101</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15582" toLevel="1" name="Fearsome Bombing">
		<!-- Powerful M. Atk. On target and nearby enemies. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>250</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15583" toLevel="1" name="Despairing Blow">
		<!-- Powerful P. Atk. on enemy. -->
		<icon>icon.skill1072</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15584" toLevel="1" name="Despairing Bombing">
		<!-- Powerful attack on nearby enemies. -->
		<icon>icon.skill0922</icon>
		<operateType>A1</operateType>
		<coolTime>200</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>3000</hitTime>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15585" toLevel="1" name="Hateful Blow">
		<!-- Swings a spark-filled chain for a powerful M. Atk. -->
		<icon>icon.skill10768</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15586" toLevel="1" name="Chains of Hatred">
		<!-- Swings a spark-filled chain for a powerful M. Atk. On target and nearby enemies. -->
		<icon>icon.skill0003</icon>
		<operateType>A1</operateType>
		<castRange>250</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>3000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15587" toLevel="1" name="High-speed Shot">
		<!-- Ranged attack on enemies to the front in a rectangular radius of you. -->
		<icon>icon.skill1086</icon>
		<operateType>A1</operateType>
		<castRange>300</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>3000</hitTime>
		<reuseDelay>10000</reuseDelay>
	</skill>
	<skill id="15588" toLevel="1" name="Soulful Blow">
		<!-- Powerful M. Atk. on enemy. -->
		<icon>icon.skill1167</icon>
		<operateType>A1</operateType>
		<castRange>250</castRange>
		<effectPoint>-2</effectPoint>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15589" toLevel="1" name="Painful Blow">
		<!-- Deals a lot of physical damage to the target. -->
		<icon>icon.skill1275</icon>
		<operateType>A1</operateType>
		<castRange>150</castRange>
		<coolTime>200</coolTime>
		<effectPoint>-2</effectPoint>
		<hitTime>1500</hitTime>
		<reuseDelay>5000</reuseDelay>
	</skill>
	<skill id="15590" toLevel="1" name="Fear of Lindvior">
		<!-- Afflicted with Fear and moving against own will. -->
		<icon>icon.skill4108</icon>
		<operateType>A2</operateType>
		<coolTime>500</coolTime>
		<effectPoint>-867</effectPoint>
		<hitTime>3000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
	</skill>
	<skill id="15592" toLevel="1" name="Dark Wind Armor">
		<!-- Reflects a part of received damage. -->
		<icon>icon.skill4095</icon>
		<operateType>A2</operateType>
		<coolTime>1500</coolTime>
		<effectPoint>100</effectPoint>
		<hitTime>3000</hitTime>
	</skill>
	<skill id="15593" toLevel="1" name="Air Strike">
		<!-- Ranged projectile. -->
		<icon>icon.skill0101</icon>
		<operateType>A1</operateType>
		<castRange>600</castRange>
		<effectPoint>-867</effectPoint>
		<hitTime>1000</hitTime>
		<isMagic>1</isMagic>
		<reuseDelay>3000</reuseDelay>
	</skill>
	<skill id="15594" toLevel="1" name="Lindvior's Speed Decrease">
		<!-- Stomping on the ground when enraged. -->
		<icon>icon.skill0127</icon>
		<operateType>A2</operateType>
		<effectPoint>-867</effectPoint>
		<hitTime>1000</hitTime>
		<isDebuff>true</isDebuff>
	</skill>
	<skill id="15595" toLevel="1" name="Lindvior's Fall">
		<!-- P. Atk. On nearby enemies. -->
		<icon>icon.skill0036</icon>
		<operateType>A2</operateType>
		<effectPoint>-574</effectPoint>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic>
	</skill>
	<skill id="15596" toLevel="1" name="Presentation - Lindvior's Takeoff">
		<!-- Lindvior takes off. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<hitTime>4200</hitTime>
	</skill>
	<skill id="15597" toLevel="1" name="Presentation - Lindvior's Landing">
		<!-- Lindvior lands. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<hitTime>5000</hitTime>
	</skill>
	<skill id="15598" toLevel="1" name="Mighty Wind Strike">
		<!-- Ranged projectile. -->
		<icon>icon.skill0000</icon>
		<operateType>A1</operateType>
		<castRange>2500</castRange>
		<effectPoint>-707</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic>
	</skill>
</list>
