# Sửa đổi hệ thống Debuff Stacking - <PERSON><PERSON><PERSON> bản cập nhật

## Vấn đề ban đầu
Trong hệ thống cũ, khi có debuff mới cùng loại (cùng AbnormalType), game chỉ so sánh `abnormalLevel` để quyết định có ghi đè hay không. Điều này dẫn đến việc:
- Debuff mới luôn reset thời gian của debuff cũ nếu có cùng hoặc cao hơn abnormal level
- Người chơi bị "spam" debuff liên tục, làm thời gian debuff bị kéo dài
- Một số debuff có `subordinationAbnormalType` có thể bypass logic hoàn toàn

## Giải pháp đã triển khai

### Thay đổi trong `EffectList.java`
Đã sửa đổi logic trong method `addActive()` để:

1. **Kiểm tra loại effect**: <PERSON><PERSON> biệt debuff và buff/effect khác
2. **Kiểm tra điều kiện ghi đè cho debuff**: 
   - <PERSON><PERSON><PERSON> thời gian còn lại của debuff hiện tại: `existingInfo.getTime()`
   - Chỉ ghi đè nếu:
     - Debuff mới có abnormal level cao hơn, HOẶC
     - Debuff hiện tại đã hết thời gian (thời gian còn lại <= 0)
3. **Xử lý subordination abnormal**: Ngăn debuff có subordination abnormal bypass logic mới

### Logic mới cho debuff:
```java
// For debuffs, only allow override if higher level or existing debuff has expired
if (skill.isDebuff())
{
    final int existingRemainingTime = existingInfo.getTime();
    
    // Override only if new effect has higher abnormal level OR existing debuff has expired
    if ((skill.getAbnormalLevel() > existingSkill.getAbnormalLevel()) || 
        (existingRemainingTime <= 0))
    {
        shouldOverride = true;
    }
}
```

### Sửa đổi subordination abnormal:
```java
// Check subordination abnormal but NOT for debuffs
if (!skill.isDebuff() && !skill.getSubordinationAbnormalType().isNone() && ...)
{
    continue; // Allow stacking for non-debuffs only
}
```

## Kết quả mong đợi

### Trước khi sửa:
- Player A stun Player B trong 10 giây
- Sau 5 giây, Player C stun Player B trong 10 giây
- Kết quả: Player B bị stun thêm 10 giây nữa (tổng 15 giây)

### Sau khi sửa:
- Player A stun Player B trong 10 giây  
- Sau 5 giây, Player C stun Player B trong 10 giây
- Kết quả: Debuff của Player C bị từ chối vì debuff của Player A vẫn còn 5 giây

### Trường hợp debuff hết thời gian:
- Player A stun Player B trong 10 giây
- Sau 10 giây (debuff hết), Player C stun Player B trong 5 giây  
- Kết quả: Debuff của Player C được áp dụng vì debuff cũ đã hết

## Tác động
- **Buff/Effect khác**: Không thay đổi, vẫn sử dụng logic cũ
- **Debuff**: Áp dụng logic mới, ngăn chặn spam debuff
- **Subordination Abnormal**: Chỉ áp dụng cho non-debuff, debuff vẫn tuân theo logic mới
- **Tương thích ngược**: Hoàn toàn tương thích với code hiện tại

## Tóm tắt logic mới
1. **Debuff cùng level, debuff hiện tại vẫn còn thời gian** → Từ chối
2. **Debuff cùng level, debuff hiện tại đã hết** → Ghi đè  
3. **Debuff level cao hơn** → Luôn ghi đè
4. **Debuff level thấp hơn** → Luôn từ chối
5. **Buff/Effect khác** → Hoạt động như cũ
6. **Debuff có subordination abnormal** → Vẫn tuân theo logic debuff mới

## Test Cases cần kiểm tra

### Test Case 1: Debuff cùng level, debuff hiện tại vẫn còn thời gian
**Setup:**
- Skill A: Stun (ID: 4726) - abnormalLevel=1, abnormalTime=5s
- Skill B: Stun (ID: 4744) - abnormalLevel=1, abnormalTime=9s

**Scenario:**
1. Player A cast Skill A lên Player C → Player C bị stun 5s
2. Sau 2s, Player B cast Skill B lên Player C
3. **Kết quả mong đợi:** Skill B bị từ chối vì debuff hiện tại vẫn còn 3s
4. Player C tiếp tục bị stun 3s như ban đầu

### Test Case 2: Debuff có subordination abnormal
**Setup:**
- Skill A: Poison debuff có subordinationAbnormalType=POISON
- Skill B: Poison debuff khác có subordinationAbnormalType=POISON

**Scenario:**
1. Player A cast Skill A → Target bị poison 10s
2. Sau 3s, Player B cast Skill B → Target vẫn còn 7s poison
3. **Kết quả mong đợi:** Skill B bị từ chối (không bypass như trước)

## Debugging
Khi `Config.DEVELOPER = true`, hệ thống sẽ log:
- "RESULT: Override (higher level: X > Y)" - khi debuff có level cao hơn được ghi đè
- "RESULT: Override (existing debuff expired, remaining: 0s)" - khi debuff cũ đã hết
- "RESULT: Rejected (existing debuff still active: Xs remaining, level: Y)" - khi debuff bị từ chối
- Thông tin subordination abnormal type để debug

## Lưu ý quan trọng
- **Subordination Abnormal**: Trước đây có thể bypass logic stacking, giờ chỉ áp dụng cho non-debuff
- **Debug logging**: Luôn hiển thị cho debuff để dễ tracking vấn đề
- **Poison/Bleeding debuffs**: Các debuff có subordination abnormal type giờ cũng tuân theo logic mới
