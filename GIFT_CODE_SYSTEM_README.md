# Hệ thống Gift Code

## Tổng quan
Hệ thống Gift Code cho phép admin tạo các mã gift code 6 ký tự và người chơi có thể sử dụng để nhận phần thưởng.

## Phần thưởng Gift Code
Khi sử dụng gift code thành công, người chơi sẽ nhận được:
- **Premium 7 ngày** cho tài khoản
- **5x Item ID 94072**
- **5x Item ID 94073** 
- **50x Item ID 49487**

## Cách sử dụng cho người chơi

### 1. Truy cập Gift Code
- Mở Community Board (Alt+B)
- Nhấn nút **"Gift Code"** ở trang chủ
- Hoặc sử dụng bypass: `_bbshome giftcode`

### 2. Nhập mã Gift Code
- Nhập mã gift code 6 ký tự vào ô text
- Nhấn nút **"Sử dụng Gift Code"**
- <PERSON><PERSON> thống sẽ thông báo kết quả

### 3. Nhận phần thưởng
- <PERSON><PERSON><PERSON> thành công, phần thưởng sẽ được thêm vào tài khoản và inventory
- Mỗi gift code chỉ có thể sử dụng 1 lần
- Sau khi sử dụng, gift code sẽ bị xóa khỏi hệ thống

## Cách sử dụng cho Admin

### 1. Tạo Gift Code
Sử dụng các lệnh admin sau:
```
//giftcode - Tạo 1 gift code
//creategiftcode - Tạo 1 gift code  
//giftcode_create - Tạo 1 gift code
//giftcode [số lượng] - Tạo nhiều gift codes (tối đa 100)
```

### 2. Quản lý và thống kê
```
//giftcode_history - Xem lịch sử sử dụng (50 gần nhất)
//giftcode_stats - Xem thống kê chi tiết
```

### 3. Ví dụ
```
//giftcode - Tạo 1 mã
//giftcode 10 - Tạo 10 mã gift code
//giftcode_history - Xem ai đã dùng gift code
//giftcode_stats - Xem thống kê tổng quan
```

## Cấu trúc Database

### Bảng gift_codes
```sql
CREATE TABLE IF NOT EXISTS `gift_codes` (
  `code` VARCHAR(6) NOT NULL,
  `used` TINYINT(1) NOT NULL DEFAULT 0,
  `created_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `used_by` INT DEFAULT NULL,
  `used_date` TIMESTAMP NULL DEFAULT NULL,
  `player_ip` VARCHAR(45) DEFAULT NULL,
  PRIMARY KEY (`code`),
  KEY `used` (`used`),
  KEY `used_by` (`used_by`),
  KEY `used_date` (`used_date`),
  KEY `player_ip` (`player_ip`)
);
```

### Bảng gift_code_history
```sql
CREATE TABLE IF NOT EXISTS `gift_code_history` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `code` VARCHAR(6) NOT NULL,
  `player_id` INT NOT NULL,
  `player_name` VARCHAR(35) NOT NULL,
  `player_ip` VARCHAR(45) DEFAULT NULL,
  `used_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `player_id` (`player_id`),
  KEY `used_date` (`used_date`),
  KEY `player_ip` (`player_ip`)
);
```

## Files đã thêm/sửa đổi

### Files mới:
1. `java/org/l2jmobius/gameserver/instancemanager/GiftCodeManager.java`
2. `dist/game/data/html/CommunityBoard/Custom/giftcode.html`
3. `dist/game/data/scripts/handlers/admincommandhandlers/AdminGiftCode.java`
4. `dist/db_installer/sql/game/gift_codes.sql`
5. `dist/db_installer/sql/game/gift_codes_sample.sql`

### Files đã sửa đổi:
1. `dist/game/data/scripts/handlers/communityboard/HomeBoard.java`
2. `dist/game/data/html/CommunityBoard/Custom/home.html`
3. `java/org/l2jmobius/gameserver/GameServer.java`

## Tính năng

### Bảo mật nâng cao
- **Atomic Transaction**: Sử dụng database transaction để tránh race condition
- **Rate Limiting**: Mỗi người chơi chỉ có thể sử dụng 1 gift code mỗi phút
- **Daily Limit**: Tối đa 5 gift codes mỗi ngày cho mỗi người chơi
- **Inventory Check**: Kiểm tra inventory đầy trước khi trao thưởng
- **IP Tracking**: Lưu trữ IP address để theo dõi
- **Usage History**: Lưu lịch sử sử dụng chi tiết
- **Synchronized Method**: Tránh nhiều người dùng cùng một mã cùng lúc
- **Auto Cleanup**: Tự động dọn dẹp dữ liệu rate limiting

### Giao diện
- Tích hợp vào Community Board
- Giao diện thân thiện, dễ sử dụng
- Hiển thị thông tin phần thưởng
- Thông báo kết quả rõ ràng

### Quản lý
- Admin có thể tạo gift code dễ dàng
- Tạo được nhiều mã cùng lúc
- Hệ thống tự động tạo mã ngẫu nhiên không trùng lặp

## Lưu ý quan trọng

### Bảo mật
- **KHÔNG** cho phép nhiều người dùng cùng một mã gift code
- Hệ thống đã được bảo vệ khỏi race conditions và spam
- Rate limiting ngăn chặn việc lạm dụng
- Lưu trữ IP để theo dõi hành vi bất thường

### Cài đặt
- Cần chạy file SQL để tạo bảng database trước khi sử dụng
- Có thể tùy chỉnh phần thưởng trong GiftCodeManager.java
- Gift codes được tạo ngẫu nhiên với 6 ký tự (A-Z, 0-9)
- Hệ thống tự động dọn dẹp dữ liệu cũ

### Hiệu suất
- Sử dụng connection pooling cho database
- Atomic transactions đảm bảo tính nhất quán
- Cleanup task tự động chạy mỗi giờ
- Optimized queries với proper indexing
